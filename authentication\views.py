# -*- coding: utf-8 -*-
"""
Authentication Views
"""

from rest_framework import status, viewsets, serializers
import requests
from urllib.parse import urlencode
from drf_spectacular.utils import extend_schema, OpenApiResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.tokens import RefreshToken, UntypedToken
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from drf_spectacular.utils import extend_schema
from django.utils import timezone
import logging
from datetime import datetime, timedelta
from rest_framework_simplejwt.utils import datetime_from_epoch
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
import uuid
from django.db import connection, transaction
import re
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from utils.pagination import CustomPagination
from django.shortcuts import render
from django.views.decorators.csrf import csrf_exempt

from .models import TcdAppMember, TcdUserConsult, TcdAppMasMemberType, TcdAppMasGovernmentSector, TcdAppMasMinistry, TcdAppMasDepartment, TcdUserConsultTeam, TcdStatisticExternalLogin

from .serializers import (
    MemberLoginSerializer,
    ConsultantLoginSerializer,
    StaffLoginSerializer,
    MemberSerializer,
    ConsultantSerializer,
    MemberRegistrationSerializer,
    TcdAppMasMemberTypeSerializer,
    TcdAppMasGovernmentSectorSerializer,
    TcdAppMasMinistrySerializer,
    TcdAppMasDepartmentSerializer,
    PasswordResetRequestSerializer,
    PasswordResetVerifyOTPSerializer,
    PasswordResetUpdatePasswordSerializer,
    ChangePasswordSerializer,
    UpdateMemberInfoSerializer,
    UpdateMemberLangSerializer,
    DeleteMemberSerializer
)

# Import updated services
from authentication.services import MemberAuthService, ConsultantAuthService, StaffAuthService, UpdateMemberInfoService, UpdateMemberLangService, UpdateConsultantLangService
from .services.otp_service import OTPService
from .constants import OTPPurpose, OTPErrorCodes, OTPStatus
from .utils import send_otp_email, get_client_ip
from django.conf import settings
import base64

# Import standard response utilities
from utils.response import APIResponse, get_language_from_request
from utils.decorators import standard_api_view

# Setup logging
logger = logging.getLogger(__name__)

# Import the new services
from authentication.services import UpdateTokenAppService, AppUsageTrackingService, AppUsageTrackingService


class NoBlacklistRefreshToken(RefreshToken):
    """
    Custom RefreshToken class that bypasses blacklist checks
    """
    def verify(self, *args, **kwargs):
        # Skip blacklist check and call parent's parent verify method
        from rest_framework_simplejwt.tokens import Token
        Token.verify(self, *args, **kwargs)
    
    def check_blacklist(self):
        # Override to do nothing
        pass


def format_jti_for_database(jti):
    """
    แปลง JTI hex string เป็น UUID format สำหรับ SQL Server
    """
    if jti and len(jti) == 32 and all(c in '0123456789abcdef' for c in jti.lower()):
        return f"{jti[:8]}-{jti[8:12]}-{jti[12:16]}-{jti[16:20]}-{jti[20:]}"
    return jti


def record_token_to_outstanding(cursor, jti, token_str, user_id, user_type, expires_at):
    """
    บันทึก token ลง outstanding token table
    """
    try:
        jti_formatted = format_jti_for_database(jti)
        cursor.execute("""
            INSERT INTO tcd_outstanding_tokens
            (jti, token, created_at, expires_at, user_id, user_type)
            VALUES (CONVERT(UNIQUEIDENTIFIER, %s), %s, %s, %s, %s, %s)
        """, [jti_formatted, token_str[:500], timezone.now(), expires_at, user_id, user_type])
        
        logger.info(f"Successfully recorded token with JTI: {jti_formatted}")
        return True
    except Exception as e:
        logger.warning(f"Failed to record token: {str(e)}")
        return False


@extend_schema(
    tags=["Member"]
)
class MemberView(viewsets.ModelViewSet):
    queryset = TcdAppMember.objects.all()
    serializer_class = MemberSerializer
    parser_classes = (MultiPartParser, FormParser, JSONParser,)
    authentication_classes = [JWTAuthentication]

    class CheckPIDSerializer(serializers.Serializer):
        pid = serializers.CharField(
            help_text="หมายเลขบัตรประชาชน",
            required=True,
        )

    class ValidateRegisterSerializer(serializers.Serializer):
        email = serializers.EmailField(
            help_text="อีเมล",
            required=True,
        )
        phone = serializers.CharField(
            help_text="เบอร์โทรศัพท์",
            required=True,
        )
        username = serializers.CharField(
            help_text="ชื่อผู้ใช้",
            required=True,
            min_length=4,
        )

    @extend_schema(
        summary="Member Registration",
        description="สมัครสมาชิก Member ใหม่ รองรับฟิลด์ทั้งหมดของ TcdAppMember และ OTP token validation",
        request=MemberRegistrationSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": True},
                    "error_message": {"type": "null"},
                    "error_code": {"type": "null"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "user": {"type": "object"},
                            "tokens": {
                                "type": "object",
                                "properties": {
                                    "access": {"type": "string"},
                                    "refresh": {"type": "string"}
                                }
                            },
                            "message": {"type": "string", "example": "สมัครสมาชิกสำเร็จ"}
                        }
                    },
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": False},
                    "error_message": {"type": "string", "example": "ข้อมูลไม่ถูกต้อง"},
                    "error_code": {"type": "integer", "example": 2001},
                    "data": {"type": "object"},
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            }
        },
    )
    @action(detail=False, methods=['post'], url_path='register', permission_classes=[AllowAny])
    def register(self, request):
        """
        API สำหรับสมัครสมาชิก Member ใหม่
        รองรับฟิลด์ทั้งหมดของ TcdAppMember และ OTP token validation

        ตัวอย่างการใช้งาน:

        POST /api/members/register/
        Content-Type: application/json

        {
            "username": "john_doe",
            "email": "<EMAIL>",
            "password": "pass123",
            "confirm_password": "pass123",
            "first_name": "จอห์น",
            "last_name": "โด",
            "name": "จอห์น โด",
            "phone": "0812345678",
            "identity_card_no": "1234567890123",
            "website": "https://johndoe.com",
            "fb_id": "facebook_id_123",
            "google_id": "google_id_456",
            "apple_id": "apple_id_789",
            "app_mas_member_type_id": 1,
            "app_mas_government_sector_id": 2,
            "app_mas_ministry_id": 3,
            "app_mas_department_id": 4,
            "app_mas_government_sector_other": "หน่วยงานอื่นๆ",
            "app_mas_ministry_other": "กระทรวงอื่นๆ",
            "app_mas_department_other": "กรมอื่นๆ",
            "is_notification": "Y",
            "lang": "th",
            "otp_token": "verified_otp_token_here"
        }

        หมายเหตุ:
        - ฟิลด์ที่จำเป็น: username, email, password, confirm_password, first_name, last_name, phone, otp_token
        - username: ความยาวขั้นต่ำ 4 ตัวอักษร, ใช้ได้เฉพาะ a-z, A-Z, 0-9, @, _, -, .
        - email: รูปแบบอีเมลที่ถูกต้อง
        - phone: ตัวเลข 10 หลักเท่านั้น
        - password: ความยาวขั้นต่ำ 4 ตัวอักษร
        - confirm_password: ต้องตรงกับ password
        - otp_token ต้องเป็น token ที่ผ่านการ verify OTP แล้ว
        - email ที่ใช้ลงทะเบียนต้องตรงกับ email ที่ใช้ในการ verify OTP
        - ฟิลด์อื่นๆ เป็น optional
        """
        logger.info("Member registration request received")
        language = get_language_from_request(request)

        # Validate input data using MemberRegistrationSerializer
        serializer = MemberRegistrationSerializer(data=request.data, context={'request': request})

        if not serializer.is_valid():
            logger.error("Member registration validation failed")
            logger.info(f"Serializer errors: {serializer.errors}")

            # ใช้ smart_validation_error เพื่อเลือก error code ที่เหมาะสม
            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )

        validated_data = serializer.validated_data
        logger.info(f"Processing member registration for: {validated_data.get('username')}")

        try:
            # Use MemberAuthService to register the member with all fields
            registration_result = MemberAuthService.register_member_full(
                validated_data=validated_data,
                request=request,
                language=language
            )

            if registration_result['success']:
                logger.info(
                    f"Member registration successful: {validated_data.get('username')}")

                return APIResponse.success(
                    data=registration_result['data'],
                    language=language
                )
            else:
                logger.error(
                    f"Member registration failed: {validated_data.get('username')} - Error: {registration_result.get('error_code')}")

                return APIResponse.error(
                    error_code=registration_result['error_code'],
                    data={},
                    language=language
                )

        except Exception as e:
            logger.error(
                f"Unexpected error during member registration: {str(e)}")
            return APIResponse.error(
                error_code=3000,  # Internal server error
                data={},
                language=language
            )

    @extend_schema(
        summary="Member Login",
        description="เข้าสู่ระบบสำหรับ Member",
        request=MemberLoginSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": True},
                    "error_message": {"type": "null"},
                    "error_code": {"type": "null"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "user": {"type": "object"},
                            "tokens": {
                                "type": "object",
                                "properties": {
                                    "access": {"type": "string"},
                                    "refresh": {"type": "string"}
                                }
                            },
                            "session_info": {"type": "object"}
                        }
                    },
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": False},
                    "error_message": {"type": "string", "example": "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง"},
                    "error_code": {"type": "integer", "example": 1001},
                    "data": {"type": "object"},
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            }
        },
    )
    @action(detail=False, methods=['post'], url_path='login', permission_classes=[AllowAny])
    def member_login(self, request):
        """
        API สำหรับ Member เข้าสู่ระบบ
        """
        logger.info("Member login request received")
        language = get_language_from_request(request)

        # Validate input data
        serializer = MemberLoginSerializer(data=request.data)

        if serializer.is_valid():
            # ตรวจสอบผลการ validate และตรวจสอบว่ามี error_code หรือไม่
            validated_data = serializer.validated_data
            member = validated_data.get('member')
            error_code = validated_data.get('error_code')

            # ถ้ามี error_code แสดงว่ามีข้อผิดพลาด (รหัสผ่านผิด หรือบัญชีถูกล็อค)
            if error_code:
                logger.info(
                    f"Login failed for user {request.data.get('username', 'unknown')}, error_code: {error_code}")

                # บันทึก action log ตาม error_code
                from authentication.services import ActionLogService

                if error_code == 1001:
                    # Invalid credentials
                    logger.info(
                        f"Invalid credentials (error_code={error_code}), returning error response")
                    ActionLogService.log_failed_login_attempt(
                        request, request.data.get('username', 'unknown'), 1001, language)
                elif error_code == 1002:
                    # Account locked
                    logger.warning(
                        f"Account locked (error_code={error_code}), returning error response")
                    if member and member.lockout_end_date:
                        logger.warning(
                            f"Member is locked until: {member.lockout_end_date}")
                    ActionLogService.log_account_locked_attempt(
                        request, member, 1002, language)
                elif error_code == 1003:
                    # Account inactive
                    logger.warning(
                        f"Account inactive (error_code={error_code}), returning error response")
                elif error_code == 1004:
                    # Username is locked (status != 1)
                    logger.warning(
                        f"Username is locked (error_code={error_code}), returning error response")

                # คืนค่า error ตาม error_code
                return APIResponse.error(
                    error_code=error_code,
                    data={},
                    language=language
                )

            # ถ้าไม่มี error_code แสดงว่า login สำเร็จ
            username = request.data.get('username')
            password = request.data.get('password')

            logger.info(f"Processing member login for: {username}")

            # Use authentication service with member from serializer
            auth_result = MemberAuthService.authenticate(
                request, username, password, member=member)

            if auth_result['success']:
                logger.info(f"Member login successful: {username}")

                return APIResponse.success(
                    data=auth_result['data'],
                    language=language
                )
            else:
                logger.error(
                    f"Member login failed: {username} - Error: {auth_result.get('error_code')}")

                return APIResponse.error(
                    error_code=auth_result['error_code'],
                    data={},  # Empty data for errors
                    language=language,
                    status_code=400
                )
        else:
            # กรณี validation error อื่นๆ (ไม่มี username หรือ password)
            logger.error("Member login validation failed")
            logger.info(f"Serializer errors: {serializer.errors}")

            error_code = 2001  # Missing required fields
            username = request.data.get('username', 'unknown')

            # บันทึก log
            from authentication.services import ActionLogService
            ActionLogService.log_failed_login_attempt(
                request, username, error_code, language)

            return APIResponse.error(
                error_code=error_code,
                data={},
                language=language
            )

    @extend_schema(
        summary="Check Identity Card Duplicate",
        description="ตรวจสอบว่า PID (หมายเลขบัตรประชาชน) มีซ้ำในระบบหรือไม่",
        request=CheckPIDSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": True},
                    "error_message": {"type": "null"},
                    "error_code": {"type": "null"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "is_duplicate": {"type": "boolean", "example": False},
                            "message": {"type": "string", "example": "PID นี้สามารถใช้งานได้"}
                        }
                    },
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": False},
                    "error_message": {"type": "string", "example": "PID นี้มีในระบบแล้ว"},
                    "error_code": {"type": "integer", "example": 2010},
                    "data": {"type": "object"},
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            }
        }
    )
    @action(detail=False, methods=['post'], url_path='check-pid', permission_classes=[AllowAny])
    def check_identity_card_duplicate(self, request):
        """
        API สำหรับตรวจสอบว่า PID (หมายเลขบัตรประชาชน) มีซ้ำในระบบหรือไม่
        """
        logger.info("Identity card duplicate check request received")
        language = get_language_from_request(request)

        # ตรวจสอบข้อมูลที่ส่งมา
        identity_card_no = request.data.get('pid')

        if not identity_card_no:
            logger.error("Identity card number not provided")
            return APIResponse.error(
                error_code=2001,  # Required field missing
                data={},
                language=language
            )

        # ตรวจสอบความซ้ำ
        logger.info(
            f"Checking duplicate for identity_card_no: {identity_card_no}")
        duplicate_check = MemberAuthService.check_identity_card_duplicate(
            identity_card_no=identity_card_no,
            language=language
        )

        # ตรวจสอบผลลัพธ์
        if duplicate_check['success']:
            # ไม่มีซ้ำ - return success
            logger.info(
                f"No duplicate found for identity_card_no: {identity_card_no}")
            return APIResponse.success(
                data=duplicate_check['data'],
                language=language
            )
        else:
            # มีซ้ำหรือเกิด error - return error
            if duplicate_check.get('existing_member'):
                logger.warning(
                    f"Duplicate identity_card_no found: {identity_card_no}")
            else:
                logger.error(
                    f"Error checking identity_card_no: {duplicate_check.get('error_message')}")

            return APIResponse.error(
                error_code=duplicate_check['error_code'],
                data=duplicate_check.get('data', {}),
                language=language,
                custom_message=duplicate_check.get('error_message')
            )

    @extend_schema(
        summary="Check Register Member",
        description="ตรวจสอบว่าผู้ใช้งานสามารถสมัครสมาชิกได้หรือไม่",
        request=ValidateRegisterSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": True},
                    "error_message": {"type": "null"},
                    "error_code": {"type": "null"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "message": {"type": "string", "example": "ok"}
                        }
                    },
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": False},
                    "error_message": {"type": "string", "example": "ชื่อผู้ใช้งานนี้มีในระบบแล้ว"},
                    "error_code": {"type": "integer", "example": 2010},
                    "data": {"type": "object"},
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            }
        }
    )
    @action(detail=False, methods=['post'], url_path='validate-register', permission_classes=[AllowAny])
    def validate_register_member(self, request):
        """
        ตรวจสอบว่าผู้ใช้งานสามารถสมัครสมาชิกได้หรือไม่
        """
        logger.info("Check register member request received")
        language = get_language_from_request(request)

        # ตรวจสอบข้อมูลที่ส่งมา
        email = request.data.get('email')
        phone = request.data.get('phone')
        username = request.data.get('username')

        if not email or not phone or not username:
            logger.error("Missing required fields")
            return APIResponse.error(
                error_code=2001,  # Required field missing
                data={},
                language=language
            )

        # ตรวจสอบ username pattern: a-z, A-Z, 0-9, @, _, -, .
        if not re.match(r'^[a-zA-Z0-9@_.-]+$', username):
            logger.error("Invalid username pattern")
            return APIResponse.error(
                error_code=2014,  # Invalid username pattern
                data={
                    "field": "username",
                },
                language=language
            )

        # ตรวจสอบ username length
        if len(username) < 4:
            logger.error("Username too short")
            return APIResponse.error(
                error_code=2015,  # Username too short
                data={
                    "field": "username",
                },
                language=language
            )

        if not re.match(r'^[0-9]{10}$', phone):
            logger.error("Invalid phone number")
            return APIResponse.error(
                error_code=2003,  # Invalid phone number
                data={},
                language=language
            )

        if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            logger.error("Invalid email address")
            return APIResponse.error(
                error_code=2002,  # Invalid email address
                data={},
                language=language
            )

        username_member_check = TcdAppMember.objects.filter(
            username=username).exists()
        username_consultant_check = TcdUserConsult.objects.filter(
            username=username).exists()
        if username_member_check or username_consultant_check:
            logger.error(f"Username {username} already exists")
            return APIResponse.error(
                error_code=2013,  # Username already exists
                data={
                    "field": "username",
                },
                language=language
            )

        email_member_check = TcdAppMember.objects.filter(email=email).exists()
        email_consultant_check = TcdUserConsult.objects.filter(
            email=email).exists()
        if email_member_check or email_consultant_check:
            logger.error(f"Email {email} already exists")
            return APIResponse.error(
                error_code=2011,  # Email already exists
                data={
                    "field": "email",
                },
                language=language
            )

        phone_member_check = TcdAppMember.objects.filter(phone=phone).exists()
        phone_consultant_check = TcdUserConsult.objects.filter(
            phone=phone).exists()
        if phone_member_check or phone_consultant_check:
            logger.error(f"Phone {phone} already exists")
            return APIResponse.error(
                error_code=2012,  # Phone already exists
                data={
                    "field": "phone",
                },
                language=language
            )

        return APIResponse.success(
            data={
                "message": "ok"
            },
            language=language
        )


@extend_schema(
    summary="Get user profile",
    description="Get user profile",
    responses={
        200: OpenApiResponse(
            description="User profile",
        ),
        404: OpenApiResponse(
            description="User not found"
        )
    },
    tags=["User"]
)
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_profile(request):
    """
    ดึงข้อมูล Profile ของผู้ใช้ที่ Login อยู่ 
    """
    language = get_language_from_request(request)

    try:
        # ตรวจสอบประเภทผู้ใช้จาก token
        user_type = getattr(request.user, 'user_type', None)
        user_id = getattr(request.user, 'id', None)
        logger.info(f"User type: {user_type}, User ID: {user_id}")

        if user_type == 'member':
            logger.info("Processing member profile request")
            # ดึงข้อมูลใหม่จากฐานข้อมูลแทนที่จะใช้ user object จาก token
            try:
                from authentication.models import TcdAppMember
                member = TcdAppMember.objects.get(id=user_id)
                member_profile = MemberAuthService.get_member_profile(member)
                return APIResponse.success(data=member_profile, language=language)
            except TcdAppMember.DoesNotExist:
                logger.error(f"Member not found in database: {user_id}")
                return APIResponse.error(
                    error_code=1000,  # User not found
                    data={},
                    language=language,
                    status_code=404
                )

        elif user_type == 'consultant':
            logger.info("Processing consultant profile request")
            # ดึงข้อมูลใหม่จากฐานข้อมูลแทนที่จะใช้ user object จาก token
            try:
                from authentication.models import TcdUserConsult
                consultant = TcdUserConsult.objects.get(id=user_id)
                consultant_profile = ConsultantAuthService.get_consultant_profile(
                    consultant)
                return APIResponse.success(data=consultant_profile, language=language)
            except TcdUserConsult.DoesNotExist:
                logger.error(f"Consultant not found in database: {user_id}")
                return APIResponse.error(
                    error_code=1000,  # User not found
                    data={},
                    language=language,
                    status_code=404
                )

        else:
            logger.error(f"Unknown or missing user type: {user_type}")
            logger.error(f"Available user attributes: {dir(request.user)}")
            return APIResponse.error(
                error_code=1000,  # User not found
                data={},  # Empty data for errors
                language=language,
                status_code=404
            )

    except Exception as e:
        logger.error(f"Error getting user profile: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        return APIResponse.error(
            error_code=3002,  # Data not found
            language=language,
            status_code=404
        )


@extend_schema(
    summary="Set PID",
    description="Set PID for member",
    responses={
        200: MemberSerializer
    },
    tags=["Member"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def set_pid(request):
    language = get_language_from_request(request)
    user_type = getattr(request.user, 'user_type', None)
    user_id = getattr(request.user, 'id', None)
    pid = request.data.get('pid')
    logger.info(f"Set PID request received: {pid} - {user_type} - {user_id}")
    if not pid:
        return APIResponse.error(
            error_code=2000,
            data={},
            language=language,
        )
    if user_type == 'member':
        member = TcdAppMember.objects.get(id=user_id)
        exist_pid = TcdAppMember.objects.filter(identity_card_no=pid).exists()
        if exist_pid :
            return APIResponse.error(
                error_code=2010,
                data={},
                language=language,
            )
        if member.identity_card_no:
            return APIResponse.error(
                error_code=5000,
                data={},
                language=language,
            )
        member.identity_card_no = pid
        member.save()
        try:
            from authentication.models import TcdAppLogs
            from django.utils import timezone
            client_ip = get_client_ip(request)
            tcd_app_log = TcdAppLogs(
                action_date=timezone.now(),
                action_log="เพิ่มข้อมูลเลขบัตรประชาชน",
                remark="สำเร็จ",
                ip_address=client_ip or "",
                app_member_id=member.id,
                name=f"{member.first_name} {member.last_name}",
                type="APPMEMBER"
            )
            tcd_app_log.save()
        except Exception as e:
            logger.error(f"Error logging app logs: {str(e)}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return APIResponse.error(
                error_code=3005,
                data={},
                language=language,
            )
        return APIResponse.success(data=MemberSerializer(member).data, language=language)
    else:
        return APIResponse.error(
            error_code=5000,
            data={},
            language=language,
        )


class UploadProfilePictureSerializer(serializers.Serializer):
    profile_picture_base64 = serializers.CharField(
        help_text="รูปโปรไฟล์ในรูปแบบ base64 string (รองรับ JPG, JPEG, PNG, GIF ขนาดไม่เกิน 5MB)",
        required=True,
    )


@extend_schema(
    summary="Upload Profile Picture",
    description="อัปโหลดรูปโปรไฟล์สำหรับ Member ด้วย Base64 string - บันทึกลง member.src",
    request=UploadProfilePictureSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "src": {"type": "string", "example": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."},
                        "base64_data": {"type": "string", "example": "/9j/4AAQSkZJRgABAQEAYABgAAD..."},
                        "mime_type": {"type": "string", "example": "image/jpeg"},
                        "file_size": {"type": "integer", "example": 12345},
                        "message": {"type": "string", "example": "อัปโหลดรูปโปรไฟล์สำเร็จ"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "รูปแบบ base64 ไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2001},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Member"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_profile_picture(request):
    """
    API สำหรับอัปโหลดรูปโปรไฟล์ของ Member ด้วย Base64 string

    Features:
    - รับรูปภาพในรูปแบบ Base64 string
    - บันทึกลง member.src ในฐานข้อมูล
    - ไม่มีการเก็บไฟล์บน server (ประหยัดพื้นที่)
    - เหมาะสำหรับ mobile application consumption
    - รองรับ JPG, JPEG, PNG, GIF (ขนาดไม่เกิน 5MB)
    """
    logger.info("Profile picture upload request received")
    language = get_language_from_request(request)

    # ตรวจสอบว่าเป็น Member หรือไม่
    user_type = getattr(request.user, 'user_type', None)
    user_id = getattr(request.user, 'id', None)

    if user_type != 'member':
        logger.error(f"Invalid user type for profile picture upload: {user_type}")
        return APIResponse.unauthorized(
            error_code=1000,  # Unauthorized
            language=language
        )

    # Validate input data using serializer
    serializer = UploadProfilePictureSerializer(data=request.data)
    
    if not serializer.is_valid():
        logger.error("Profile picture upload validation failed")
        logger.info(f"Serializer errors: {serializer.errors}")
        
        return APIResponse.smart_validation_error(
            errors=serializer.errors,
            language=language
        )

    profile_picture_base64 = serializer.validated_data['profile_picture_base64']
    logger.info(f"Processing profile picture upload for member: {user_id}")

    # ใช้ MemberAuthService สำหรับอัปโหลด
    upload_result = MemberAuthService.upload_profile_picture_base64(
        member_id=user_id,
        base64_string=profile_picture_base64,
        request=request,
        language=language
    )

    if upload_result['status']:
        logger.info(f"Profile picture upload successful for member: {user_id}")
        return APIResponse.success(
            data=upload_result['data'],
            language=language
        )
    else:
        logger.error(f"Profile picture upload failed for member: {user_id} - Error: {upload_result.get('error_code')}")
        return APIResponse.error(
            error_code=upload_result['error_code'],
            data={},
            language=language
        )


@extend_schema(
    tags=["Consultant"]
)
class ConsultantView(viewsets.ModelViewSet):
    queryset = TcdUserConsult.objects.all()
    serializer_class = ConsultantSerializer
    parser_classes = (JSONParser,)
    authentication_classes = [JWTAuthentication]

    @extend_schema(
        summary="Consultant Login",
        description="เข้าสู่ระบบสำหรับ Consultant",
        request=ConsultantLoginSerializer,
        responses={
            200: {
                "type": "object",
                "properties": {
                    "status": {"type": "boolean", "example": True},
                    "error_message": {"type": "null"},
                    "error_code": {"type": "null"},
                    "data": {
                        "type": "object",
                        "properties": {
                            "user": {"type": "object"},
                            "tokens": {
                                "type": "object",
                                "properties": {
                                    "access": {"type": "string"},
                                    "refresh": {"type": "string"}
                                }
                            },
                            "session_info": {"type": "object"}
                        }
                    },
                    "api_version": {"type": "string", "example": "v.0.0.1"}
                }
            }
        },
    )
    @action(detail=False, methods=['post'], url_path='login', permission_classes=[AllowAny])
    def consultant_login(self, request):
        """
        API สำหรับ Consultant เข้าสู่ระบบ
        """
        logger.info("Consultant login request received")
        language = get_language_from_request(request)

        logger.info(f"Request data: {request.data}")

        # Validate input data
        serializer = ConsultantLoginSerializer(data=request.data)

        if not serializer.is_valid():
            logger.error("Consultant login validation failed")
            logger.info(f"Serializer errors: {serializer.errors}")

            # ดึง error code จาก serializer.errors
            error_code = 2009  # default validation error
            # ดึง username จาก request data
            username = request.data.get('username', 'unknown')

            # serializer.errors มักจะมี structure แบบ ValidationError ที่ซับซ้อนกว่า
            errors = serializer.errors
            logger.info(f"Error structure: {type(errors)} - {errors}")

            # ตรวจสอบ non_field_errors ซึ่งมาจาก validate() method
            if hasattr(errors, 'get') and 'non_field_errors' in errors:
                non_field_errors = errors['non_field_errors']
                logger.info(f"Non-field errors: {non_field_errors}")

                if isinstance(non_field_errors, list) and len(non_field_errors) > 0:
                    error_detail = non_field_errors[0]
                    logger.error(f"First error detail: {type(error_detail)} - {error_detail}")

                    # ตรวจสอบว่า error เป็น dict ที่มี code หรือไม่
                    if isinstance(error_detail, dict):
                        if 'code' in error_detail:
                            error_code_str = error_detail['code']
                            if error_code_str == 'invalid_credentials':
                                error_code = 1001
                            elif error_code_str == 'account_unverified':
                                error_code = 1003
                            elif error_code_str == 'missing_credentials':
                                error_code = 2001
                    # หรืออาจจะเป็น string ที่มี code อยู่ใน message
                    elif isinstance(error_detail, str):
                        if 'invalid_credentials' in error_detail:
                            error_code = 1001
                        elif 'account_unverified' in error_detail:
                            error_code = 1003
                        elif 'missing_credentials' in error_detail:
                            error_code = 2001

            logger.info(f"Final error code: {error_code}")

            # บันทึก action log เมื่อเกิด error_code 1001
            if error_code == 1001:
                from authentication.services import ActionLogService
                from authentication.models import TcdAppLogs
                
                user_consult = TcdUserConsult.objects.get(username=username)
                if user_consult:
                    TcdAppLogs.objects.create(
                        action_date=timezone.now(),
                        action_log='พยายามลงชื่อเข้าใช้งาน',
                        remark='รหัสผ่านไม่ถูกต้อง',
                        ip_address=get_client_ip(request) or '',
                        user_consult_id=user_consult.id,
                        app_member_id=None,
                        name=user_consult.username,
                        type='APPCONSULTANT'
                    )
                else:
                    ActionLogService.log_failed_login_attempt(
                        request, username, 1001, language)

            return APIResponse.error(
                error_code=error_code,
                data={},
                language=language,
            )

        username = serializer.validated_data['username']
        password = serializer.validated_data['password']

        logger.info(f"Processing consultant login for: {username}")

        # Use authentication service
        auth_result = ConsultantAuthService.authenticate(
            request, username, password)

        if auth_result['success']:
            logger.info(f"Consultant login successful: {username}")

            return APIResponse.success(
                data=auth_result['data'],
                language=language
            )
        else:
            logger.error(
                f"Consultant login failed: {username} - Error: {auth_result.get('error_code')}")

            return APIResponse.error(
                error_code=auth_result['error_code'],
                data={},  # Empty data for errors
                language=language,
                status_code=400
            )


@extend_schema(
    summary="Staff Login",
    description="เข้าสู่ระบบสำหรับ Staff (TcdUsers)",
    request=StaffLoginSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "user": {"type": "object"},
                        "tokens": {
                            "type": "object",
                            "properties": {
                                "access": {"type": "string"},
                                "refresh": {"type": "string"}
                            }
                        },
                        "session_info": {"type": "object"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 1001},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Staff"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def staff_login(request):
    """
    API สำหรับเข้าสู่ระบบของ Staff
    
    ตัวอย่างการใช้งาน:
    
    POST /api/auth/staff/login/
    Content-Type: application/json
    
    {
        "username": "staff_username",
        "password": "staff_password"
    }
    """
    language = get_language_from_request(request)
    
    logger.info("Processing staff login request")
    
    # Validate input data
    serializer = StaffLoginSerializer(data=request.data)
    if not serializer.is_valid():
        # Check if there's an error_code in validated_data
        error_code = serializer.validated_data.get('error_code') if hasattr(serializer, 'validated_data') else None
        
        if not error_code:
            # Regular validation errors
            logger.warning(f"Staff login validation failed: {serializer.errors}")
            return APIResponse.validation_error(
                serializer.errors,
                language=language
            )
        
        # Error code from serializer validation
        return APIResponse.error(
            error_code=error_code,
            data={},
            language=language,
        )

    username = serializer.validated_data['username']
    password = serializer.validated_data['password']

    logger.info(f"Processing staff login for: {username}")

    # Use authentication service
    auth_result = StaffAuthService.authenticate(
        request, username, password)

    if auth_result['success']:
        logger.info(f"Staff login successful: {username}")

        return APIResponse.success(
            data=auth_result['data'],
            language=language
        )
    else:
        logger.error(
            f"Staff login failed: {username} - Error: {auth_result.get('error_code')}")

        return APIResponse.error(
            error_code=auth_result['error_code'],
            data={},  # Empty data for errors
            language=language,
            status_code=400
        )


class TokenSerializer(serializers.Serializer):
    token = serializers.CharField()


@extend_schema(
    summary="Refresh Token",
    description="ต่ออายุ Access Token ด้วย Refresh Token และ revoke access token เก่าออกด้วย",
    request=TokenSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "tokens": {
                            "type": "object",
                            "properties": {
                                "access": {"type": "string"},
                                "refresh": {"type": "string"}
                            }
                        },
                        "message": {"type": "string", "example": "ต่ออายุโทเค็นสำเร็จ"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "โทเค็นไม่ถูกต้องหรือหมดอายุ"},
                "error_code": {"type": "integer", "example": 1005},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Token Management"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_token(request):
    """
    ต่ออายุ Access Token ด้วย Refresh Token
    """
    refresh_token_str = request.data.get('token')
    language = get_language_from_request(request)

    logger.info("Processing refresh token request")
    logger.info(f"Token provided: {bool(refresh_token_str)}")

    if not refresh_token_str:
        logger.warning("Refresh token not provided")
        return APIResponse.error(
            error_code=2001,  # Required field missing
            data={},  # Empty data for errors
            language=language
        )

    try:
        # ใช้ UntypedToken เพื่อหลีกเลี่ยง blacklist check อัตโนมัติ
        logger.info("Attempting to decode refresh token with UntypedToken")
        untyped_token = UntypedToken(refresh_token_str)

        # ตรวจสอบว่าเป็น refresh token จริงหรือไม่
        token_type = untyped_token.get('token_type')
        if token_type != 'refresh':
            logger.error(f"Invalid token type: {token_type}, expected 'refresh'")
            return APIResponse.error(
                error_code=1005,  # Invalid token
                data={},
                language=language,
                status_code=400
            )

        logger.info("Refresh token type validated successfully")

        # ดึงข้อมูล user จาก refresh token
        user_id = untyped_token.get('user_id')
        username = untyped_token.get('username')
        user_type = untyped_token.get('user_type')
        email = untyped_token.get('email')
        first_name = untyped_token.get('first_name')
        last_name = untyped_token.get('last_name')

        logger.info(f"User data from token: user_id={user_id}, username={username}, user_type={user_type}")

        # ตรวจสอบ blacklist ด้วยตนเอง
        from rest_framework_simplejwt.settings import api_settings
        jti = untyped_token.get(api_settings.JTI_CLAIM)

        if not jti:
            logger.error("No JTI found in token")
            return APIResponse.error(
                error_code=1005,  # Invalid token
                data={},
                language=language,
                status_code=400
            )

        # แปลง jti ให้เป็น UUID format ที่ SQL Server เข้าใจ
        jti_str = str(jti)
        jti_for_db = format_jti_for_database(jti_str)
        logger.info(f"Formatted JTI for database: {jti_for_db}")

        # ตรวจสอบ blacklist ด้วย direct SQL query
        logger.info(f"Checking blacklist for jti: {jti_for_db}")
        with connection.cursor() as cursor:
            try:
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM tcd_blacklisted_tokens bt
                    INNER JOIN tcd_outstanding_tokens ot ON bt.token_id = ot.id
                    WHERE ot.jti = CONVERT(UNIQUEIDENTIFIER, %s)
                """, [jti_for_db])

                result = cursor.fetchone()
                is_blacklisted = result[0] > 0 if result else False

                if is_blacklisted:
                    logger.error(f"Token is blacklisted: {jti_for_db}")
                    return APIResponse.error(
                        error_code=1005,  # Invalid token
                        data={},
                        language=language,
                        status_code=400
                    )

                logger.info(f"Token not in blacklist: {jti_for_db}")

            except Exception as blacklist_error:
                logger.error(f"Error checking blacklist: {str(blacklist_error)}")
                # ถ้าตรวจสอบ blacklist ไม่ได้ ให้ดำเนินการต่อ (fail open)
                logger.warning("Blacklist check failed, proceeding with token refresh")

        # สร้าง access token ใหม่โดยไม่ผ่าน blacklist check
        logger.info("Creating new access token")
        
        from rest_framework_simplejwt.tokens import AccessToken
        access_token = AccessToken()
        access_token.payload.update({
            'user_id': user_id,
            'username': username,
            'user_type': user_type,
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'token_type': 'access'
        })

        # สร้าง access token ใหม่
        new_access_token = str(access_token)
        logger.info("New access token generated")

        # Blacklist token เดิมด้วยวิธี manual
        logger.info("Manually blacklisting old refresh token")
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # บันทึก outstanding token ถ้ายังไม่มี
                    cursor.execute("""
                        SELECT id FROM tcd_outstanding_tokens
                        WHERE jti = CONVERT(UNIQUEIDENTIFIER, %s)
                    """, [jti_for_db])

                    existing_token = cursor.fetchone()

                    if existing_token:
                        token_id = existing_token[0]
                        logger.info(f"Found existing outstanding token with ID: {token_id}")
                    else:
                        # สร้าง outstanding token ใหม่
                        now = timezone.now()
                        try:
                            token_exp = untyped_token.get('exp')
                            if token_exp:
                                expires = datetime.fromtimestamp(token_exp, tz=timezone.utc)
                            else:
                                expires = now + timezone.timedelta(days=30)
                        except:
                            expires = now + timezone.timedelta(days=30)

                        cursor.execute("""
                            INSERT INTO tcd_outstanding_tokens
                            (jti, token, created_at, expires_at, user_id, user_type)
                            OUTPUT INSERTED.id
                            VALUES (CONVERT(UNIQUEIDENTIFIER, %s), %s, %s, %s, %s, %s)
                        """, [jti_for_db, refresh_token_str[:500], now, expires, user_id, user_type])

                        result = cursor.fetchone()
                        if not result or result[0] is None:
                            logger.error("Failed to create outstanding token")
                            raise Exception("Failed to create outstanding token")

                        token_id = int(result[0])
                        logger.info(f"Created new outstanding token with ID: {token_id}")

                    # ตรวจสอบว่า token ถูก blacklist แล้วหรือยัง
                    cursor.execute("""
                        SELECT COUNT(*) FROM tcd_blacklisted_tokens
                        WHERE token_id = %s
                    """, [token_id])

                    blacklist_exists = cursor.fetchone()[0] > 0

                    if not blacklist_exists:
                        # บันทึก blacklisted token
                        cursor.execute("""
                            INSERT INTO tcd_blacklisted_tokens
                            (token_id, blacklisted_at)
                            VALUES (%s, %s)
                        """, [token_id, timezone.now()])

                        logger.info(f"Successfully blacklisted token with ID: {token_id}")
                    else:
                        logger.info(f"Token with ID {token_id} is already blacklisted")

        except Exception as blacklist_error:
            logger.error(f"Error during manual blacklisting: {str(blacklist_error)}")
            # ถ้า blacklist ไม่ได้ ให้ดำเนินการต่อ (fail open)
            logger.warning("Manual blacklisting failed, proceeding with token refresh")

        # สร้าง refresh token ใหม่พร้อมข้อมูล user
        new_refresh = NoBlacklistRefreshToken()

        # คัดลอกข้อมูล user ไปยัง refresh token ใหม่
        if user_id is not None:
            new_refresh['user_id'] = user_id
        if username is not None:
            new_refresh['username'] = username
        if user_type is not None:
            new_refresh['user_type'] = user_type
        if email is not None:
            new_refresh['email'] = email
        if first_name is not None:
            new_refresh['first_name'] = first_name
        if last_name is not None:
            new_refresh['last_name'] = last_name

        new_refresh_token = str(new_refresh)
        logger.info("New refresh token generated with user data")

        # บันทึก token ใหม่ทั้งสองตัวลง outstanding token table เพื่อให้สามารถ revoke ได้ในอนาคต
        logger.info("Recording new tokens for future revocation")
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # บันทึก refresh token ใหม่
                    refresh_jti = new_refresh.payload.get('jti')
                    if refresh_jti:
                        refresh_exp = new_refresh.payload.get('exp')
                        if refresh_exp:
                            refresh_expires = datetime.fromtimestamp(refresh_exp, tz=timezone.utc)
                        else:
                            refresh_expires = timezone.now() + timezone.timedelta(days=30)  # default 30 days
                        
                        record_token_to_outstanding(cursor, refresh_jti, new_refresh_token, user_id, user_type, refresh_expires)

                    # บันทึก access token ใหม่
                    access_jti = access_token.payload.get('jti')
                    if access_jti:
                        access_exp = access_token.payload.get('exp')
                        if access_exp:
                            access_expires = datetime.fromtimestamp(access_exp, tz=timezone.utc)
                        else:
                            access_expires = timezone.now() + timezone.timedelta(hours=1)  # default 1 hour
                        
                        record_token_to_outstanding(cursor, access_jti, new_access_token, user_id, user_type, access_expires)
                        
        except Exception as record_error:
            logger.warning(f"Failed to record new tokens: {str(record_error)}")
            # ไม่ให้ error นี้หยุดการทำงาน

        # Revoke access tokens เก่าที่เกี่ยวข้องกับ user นี้ (ยกเว้น token ที่เพิ่งสร้าง)
        logger.info("Revoking old access tokens for this user")
        try:
            with transaction.atomic():
                with connection.cursor() as cursor:
                    now = timezone.now()
                    new_access_jti = access_token.payload.get('jti')
                    
                    # แปลง JTI ของ token ใหม่เป็น UUID format เพื่อเปรียบเทียบ
                    new_access_jti_formatted = format_jti_for_database(new_access_jti)
                    new_refresh_jti = new_refresh.payload.get('jti')
                    new_refresh_jti_formatted = format_jti_for_database(new_refresh_jti)

                    # หา outstanding tokens ที่เป็นของ user เดียวกันและยังไม่ถูก blacklist
                    # ยกเว้น refresh token เก่า, refresh token ใหม่, และ access token ใหม่
                    cursor.execute("""
                        SELECT ot.id, ot.jti, ot.created_at
                        FROM tcd_outstanding_tokens ot
                        LEFT JOIN tcd_blacklisted_tokens bt ON ot.id = bt.token_id
                        WHERE ot.user_id = %s 
                        AND ot.user_type = %s
                        AND ot.expires_at > %s
                        AND bt.id IS NULL
                        AND ot.jti != CONVERT(UNIQUEIDENTIFIER, %s)
                        AND ot.jti != CONVERT(UNIQUEIDENTIFIER, %s)
                        AND ot.jti != CONVERT(UNIQUEIDENTIFIER, %s)
                        AND ot.created_at < %s
                    """, [user_id, user_type, now, jti_for_db, new_access_jti_formatted or '', new_refresh_jti_formatted or '', now])
                    
                    old_tokens = cursor.fetchall()
                    
                    if old_tokens:
                        logger.info(f"Found {len(old_tokens)} old tokens to revoke")
                        
                        for token_id, jti, created_at in old_tokens:
                            try:
                                # Blacklist token เก่า
                                cursor.execute("""
                                    INSERT INTO tcd_blacklisted_tokens
                                    (token_id, blacklisted_at)
                                    VALUES (%s, %s)
                                """, [token_id, now])
                                
                                logger.info(f"Successfully blacklisted old token with ID: {token_id}, JTI: {jti}")
                                
                            except Exception as blacklist_error:
                                logger.warning(f"Failed to blacklist token {token_id}: {str(blacklist_error)}")
                                # ไม่ให้ error นี้หยุดการทำงาน ให้ดำเนินการต่อ
                                continue
                    else:
                        logger.info("No old tokens found to revoke")
                        
        except Exception as revoke_error:
            logger.warning(f"Error during old token revocation: {str(revoke_error)}")
            # ไม่ให้ error นี้หยุดการทำงาน ให้ดำเนินการต่อ
            logger.info("Proceeding with token refresh despite token revocation error")

        return APIResponse.success(
            data={
                "tokens": {
                    "access": new_access_token,
                    "refresh": new_refresh_token
                },
                "message": "ต่ออายุโทเค็นสำเร็จและ revoke token เก่าแล้ว"
            },
            language=language
        )

    except TokenError as e:
        logger.error(f"Token error during refresh: {str(e)}")
        from utils.response import service_error_response
        error_response = service_error_response(1005, language)  # Invalid token
        return APIResponse.error(
            error_code=error_response['error_code'],
            data={},
            language=language,
            status_code=400
        )
    except Exception as e:
        logger.error(f"Unexpected error during token refresh: {str(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        from utils.response import service_error_response
        error_response = service_error_response(1000, language)  # General error
        return APIResponse.server_error(
            error_code=error_response['error_code'],
            language=language
        )


@extend_schema(
    summary="Logout",
    description="ออกจากระบบ - ทำให้ token หมดอายุ (รองรับทั้ง refresh และ access token)",
    request=TokenSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "ออกจากระบบสำเร็จ"},
                        "token_type": {"type": "string", "example": "refresh"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "โทเค็นไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 1005},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Token Management"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def logout(request):
    """
    ออกจากระบบ - ทำให้ token หมดอายุ (รองรับทั้ง refresh และ access token)
    """
    token_str = request.data.get('token')
    language = get_language_from_request(request)

    logger.info("Processing logout request")
    logger.info(f"Request data keys: {list(request.data.keys())}")
    logger.info(f"Token provided: {bool(token_str)}")

    if not token_str:
        logger.warning("Token not provided in logout request")
        return APIResponse.error(
            error_code=2001,  # Required field missing
            data={},
            language=language
        )

    logger.info(f"Token length: {len(token_str)}")
    logger.info(f"Token prefix: {token_str[:20]}...")

    try:
        # ใช้ UntypedToken ตั้งแต่เริ่มต้นเพื่อหลีกเลี่ยง blacklist check อัตโนมัติ
        logger.info("Attempting to decode token with UntypedToken")
        token = UntypedToken(token_str)
        logger.info("Successfully decoded token")

        # ดึงข้อมูลจากโทเค็น
        user_id = token.get('user_id', 0)
        user_type = token.get('user_type', 'unknown')
        token_type = token.get('token_type', 'unknown')
        
        if user_type == 'member':
            member = TcdAppMember.objects.filter(id=user_id)
            if member.exists():
                member.update(token_app=None)
        elif user_type == 'consultant':
            consultant = TcdUserConsult.objects.filter(id=user_id)
            if consultant.exists():
                consultant.update(token_app=None)

        logger.info(
            f"Token payload: user_id={user_id}, user_type={user_type}, token_type={token_type}")

        # ใช้ jti จริงจาก token
        from rest_framework_simplejwt.settings import api_settings
        jti = token.get(api_settings.JTI_CLAIM)

        if not jti:
            logger.error("No JTI found in token")
            raise Exception("No JTI found in token")

        logger.info(
            f"Original JTI from token: {jti} (type: {type(jti)}, length: {len(str(jti))})")

        # แปลง jti ให้เป็น string และจัดการรูปแบบต่างๆ
        jti_str = str(jti)

        # ตรวจสอบและแปลง JTI format
        if len(jti_str) == 32 and all(c in '0123456789abcdef' for c in jti_str.lower()):
            # แปลง hex string เป็น UUID format
            formatted_jti = f"{jti_str[:8]}-{jti_str[8:12]}-{jti_str[12:16]}-{jti_str[16:20]}-{jti_str[20:]}"
            logger.info(
                f"Converted hex jti {jti_str} to UUID format {formatted_jti}")
            jti_for_db = formatted_jti
        elif len(jti_str) == 36 and jti_str.count('-') == 4:
            # อาจจะเป็น UUID format อยู่แล้ว
            logger.info(f"JTI appears to be in UUID format already: {jti_str}")
            jti_for_db = jti_str
        else:
            # กรณีอื่นๆ ลองใช้ as-is
            logger.warning(f"Unknown JTI format: {jti_str}, using as-is")
            jti_for_db = jti_str

        logger.info(f"Final JTI for database: {jti_for_db}")

        # สำหรับ refresh token ให้ลองใช้ built-in blacklist ก่อน (แต่ต้อง handle error)
        if token_type == 'refresh':
            try:
                logger.info(
                    "Token is refresh type, attempting built-in blacklist")
                # สร้าง RefreshToken object และ blacklist (อาจจะ error)
                # ใช้ UntypedToken แทนเพื่อหลีกเลี่ยง blacklist check
                untyped_refresh = UntypedToken(token_str)
                
                # สร้าง RefreshToken object ใหม่และคัดลอกข้อมูล
                refresh = NoBlacklistRefreshToken()
                refresh.payload.update(untyped_refresh.payload)
                refresh.blacklist()
                logger.info(
                    "Successfully blacklisted refresh token with built-in method")

                return APIResponse.success(
                    data={
                        "message": "ออกจากระบบสำเร็จ",
                        "token_type": "refresh"
                    },
                    language=language
                )
            except Exception as refresh_error:
                logger.warning(
                    f"Built-in refresh blacklist failed: {str(refresh_error)}, using manual method")
                # ถ้า built-in ไม่ได้ ให้ใช้วิธี manual

        # ใช้ manual blacklisting (สำหรับ access token หรือ refresh token ที่ built-in ไม่ได้)
        logger.info("Using manual blacklisting method")
        logger.info("Starting database transaction")
        with transaction.atomic():
            with connection.cursor() as cursor:
                # ทดสอบการแปลง UUID ก่อน
                try:
                    logger.info(f"Testing UUID conversion for: {jti_for_db}")
                    cursor.execute(
                        "SELECT CONVERT(UNIQUEIDENTIFIER, %s) as test_uuid", [jti_for_db])
                    test_result = cursor.fetchone()
                    logger.info(
                        f"UUID conversion test successful: {test_result[0] if test_result else 'None'}")
                except Exception as uuid_error:
                    logger.error(
                        f"UUID conversion test failed: {str(uuid_error)}")
                    logger.error(f"Failed JTI value: '{jti_for_db}'")

                    # ลองใช้ NEWID() แทน
                    logger.info("Using NEWID() as fallback for JTI")
                    cursor.execute("SELECT NEWID() as new_jti")
                    result = cursor.fetchone()
                    jti_for_db = str(
                        result[0]) if result else str(uuid.uuid4())
                    logger.info(f"Generated new JTI: {jti_for_db}")

                # บันทึกโทเค็นลงฐานข้อมูล
                now = timezone.now()
                # ใช้ expires_at จาก token จริง หรือ default เป็น 1 วันจากตอนนี้
                try:
                    token_exp = token.get('exp')
                    if token_exp:
                        expires = datetime.fromtimestamp(token_exp, tz=timezone.utc)
                    else:
                        expires = now + timezone.timedelta(days=1)
                except:
                    expires = now + timezone.timedelta(days=1)

                try:
                    logger.info("Inserting outstanding token")
                    # บันทึก outstanding token ก่อน
                    cursor.execute(
                        """
                        INSERT INTO tcd_outstanding_tokens 
                        (jti, token, created_at, expires_at, user_id, user_type)
                        OUTPUT INSERTED.id
                        VALUES (CONVERT(UNIQUEIDENTIFIER, %s), %s, %s, %s, %s, %s)
                        """,
                        [jti_for_db, token_str[:500], now,
                            expires, user_id, user_type]
                    )

                    # ดึง ID ที่สร้างขึ้น
                    result = cursor.fetchone()
                    if not result or result[0] is None:
                        logger.error("Failed to get inserted token ID")
                        raise Exception("Failed to get inserted token ID")

                    token_id = int(result[0])
                    logger.info(
                        f"Created outstanding token with ID: {token_id}")

                    # บันทึก blacklisted token
                    logger.info("Inserting blacklisted token")
                    cursor.execute(
                        """
                        INSERT INTO tcd_blacklisted_tokens 
                        (token_id, blacklisted_at) 
                        VALUES (%s, %s)
                        """,
                        [token_id, now]
                    )

                    logger.info(
                        f"Successfully blacklisted token with ID: {token_id}")

                except Exception as sql_error:
                    logger.error(
                        f"SQL error during token blacklisting: {str(sql_error)}")
                    raise

        logger.info("Database transaction completed successfully")

        return APIResponse.success(
            data={
                "message": "ออกจากระบบสำเร็จ",
                "token_type": token_type
            },
            language=language
        )

    except Exception as e:
        logger.error(f"Error during logout: {str(e)}")
        logger.error(f"Exception type: {type(e)}")
        import traceback
        logger.error(f"Full traceback: {traceback.format_exc()}")
        return APIResponse.error(
            error_code=1005,  # Invalid token
            data={},
            language=language,
            status_code=400
        )


@extend_schema(
    summary="Revoke Token",
    description="เพิกถอน Token โดยผู้ดูแลระบบ - ทำให้ token หมดอายุทันที",
    request={
        "type": "object",
        "properties": {
            "token": {"type": "string", "description": "Token ที่ต้องการเพิกถอน (refresh หรือ access)"}
        },
        "required": ["token"]
    },
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "เพิกถอนโทเค็นสำเร็จ"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "โทเค็นไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 1005},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Token Management"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def revoke_token(request):
    """
    เพิกถอน Token (Revoke) - ทำให้ token หมดอายุทันที
    สามารถใช้กับทั้ง refresh และ access token
    """
    token_str = request.data.get('token')
    language = get_language_from_request(request)

    logger.info(f"Processing token revocation request")

    if not token_str:
        logger.warning("Token not provided in request")
        return APIResponse.error(
            error_code=2001,  # Required field missing
            data={},
            language=language
        )

    try:
        # ลองถอดรหัสโทเค็นก่อน
        try:
            # ทดลองถอดรหัสโทเค็นแบบไม่ตรวจสอบประเภท
            token = UntypedToken(token_str)

            # ดึงข้อมูลจากโทเค็น
            user_id = token.get('user_id', 0)
            user_type = token.get('user_type', 'unknown')
            token_type = token.get('token_type', 'unknown')

            # ใช้ jti จริงจาก token แทนการสร้างใหม่
            from rest_framework_simplejwt.settings import api_settings
            jti = token.get(api_settings.JTI_CLAIM)

            if not jti:
                logger.error("No JTI found in token")
                raise Exception("No JTI found in token")

            # แปลง jti hex string เป็นรูปแบบ UUID ที่ SQL Server เข้าใจ
            # jti จาก JWT มักจะเป็น hex string แบบ: 7344b4840b804f5192dde2e9e9337271
            # ต้องแปลงเป็น UUID format: 7344b484-0b80-4f51-92dd-e2e9e9337271
            if len(jti) == 32 and all(c in '0123456789abcdef' for c in jti.lower()):
                # แปลง hex string เป็น UUID format
                formatted_jti = f"{jti[:8]}-{jti[8:12]}-{jti[12:16]}-{jti[16:20]}-{jti[20:]}"
                logger.info(
                    f"Converted hex jti {jti} to UUID format {formatted_jti}")
                jti = formatted_jti

            logger.info(
                f"Token decoded: user_id={user_id}, type={user_type}, token_type={token_type}, jti={jti}")

            # ใช้ direct SQL เพื่อบันทึกข้อมูล
            with transaction.atomic():
                with connection.cursor() as cursor:
                    # ทดลองแปลงค่า UUID เพื่อดีบั๊ก
                    try:
                        cursor.execute(
                            "SELECT CONVERT(UNIQUEIDENTIFIER, %s)", [jti])
                        logger.info("JTI conversion test passed")
                    except Exception as e:
                        logger.error(f"JTI test failed: {str(e)}")
                        raise

                    # บันทึกโทเค็นลงฐานข้อมูล
                    try:
                        # สร้าง outstanding token ใหม่เสมอ
                        now = timezone.now()
                        expires = now + timezone.timedelta(hours=1)

                        # บันทึก outstanding token ก่อน และใช้ OUTPUT clause
                        cursor.execute(
                            """
                            INSERT INTO tcd_outstanding_tokens 
                            (jti, token, created_at, expires_at, user_id, user_type)
                            OUTPUT INSERTED.id
                            VALUES (CONVERT(UNIQUEIDENTIFIER, %s), %s, %s, %s, %s, %s)
                            """,
                            [jti, token_str[:500], now, expires, user_id, user_type]
                        )

                        # ดึง ID ที่สร้างขึ้นจาก OUTPUT
                        result = cursor.fetchone()

                        if not result or result[0] is None:
                            logger.error(
                                "Failed to get inserted token ID from OUTPUT clause")
                            raise Exception(
                                "Failed to get inserted token ID from OUTPUT clause")

                        token_id = int(result[0])
                        logger.info(
                            f"Created outstanding token with ID: {token_id}")

                        # บันทึก blacklisted token
                        cursor.execute(
                            """
                            INSERT INTO tcd_blacklisted_tokens 
                            (token_id, blacklisted_at) 
                            VALUES (%s, %s)
                            """,
                            [token_id, now]
                        )

                        logger.info(
                            f"Successfully blacklisted token with ID: {token_id}")

                    except Exception as sql_error:
                        logger.error(
                            f"SQL error in blacklist: {str(sql_error)}")
                        raise

        except (TokenError, InvalidToken) as e:
            logger.error(f"Failed to decode token: {str(e)}")
            raise

        return APIResponse.success(
            data={
                "message": "เพิกถอนโทเค็นสำเร็จ",
                "token_type": token_type if 'token_type' in locals() else 'unknown'
            },
            language=language
        )

    except Exception as e:
        logger.error(f"Error revoking token: {str(e)}")
        return APIResponse.error(
            error_code=1005,  # Invalid token
            data={},
            language=language,
            status_code=400
        )


@extend_schema(
    summary="Generate OTP",
    description="Generate OTP for specified purpose (login, registration, etc.)",
    request={
        "type": "object",
        "properties": {
            "identifier": {"type": "string", "description": "User identifier (email)"},
            "purpose": {"type": "string", "description": "Purpose of OTP (login, registration, reset_password, etc.)"}
        },
        "required": ["identifier"]
    },
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "token": {"type": "string"},
                        "expires_at": {"type": "string", "format": "date-time"},
                        "message": {"type": "string", "example": "OTP has been sent to your email"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "Invalid email format"},
                "error_code": {"type": "integer", "example": 2002},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["OTP"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def generate_otp(request):
    """
    API สำหรับสร้าง OTP สำหรับการยืนยันตัวตน
    """
    logger.info("Generate OTP request received")
    language = get_language_from_request(request)

    # ตรวจสอบข้อมูลที่ส่งมา
    email = request.data.get('email')
    purpose = request.data.get('purpose', OTPPurpose.REGISTER)

    if not email:
        logger.error("Email not provided")
        return APIResponse.error(
            error_code=2001,  # Required field missing
            data={},
            language=language
        )

    # ตรวจสอบรูปแบบอีเมล
    if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
        logger.error("Invalid email address format")
        return APIResponse.error(
            error_code=2002,  # Invalid email format
            data={},
            language=language
        )

    # สร้าง OTP และ JWT token
    otp_result = OTPService.generate_otp(email, purpose)

    # ส่ง OTP ทางอีเมล
    otp_code = otp_result['otp']
    ref_code = otp_result['ref_code']
    email_result = send_otp_email(email, otp_code, ref_code, purpose, language)

    if not email_result['success']:
        logger.error(f"Failed to send OTP email: {email_result['error']}")
        return APIResponse.error(
            error_code=OTPErrorCodes.OTP_GENERATION_FAILED,
            data={},
            language=language,
        )

    # ส่งเฉพาะ token และ expires_at กลับไป (ไม่ส่ง OTP กลับไปด้วย)
    return APIResponse.success(
        data={
            "token": otp_result['token'],
            "ref_code": ref_code,
            "expires_at": otp_result['expires_at'].isoformat(),
            "message": "OTP ได้ถูกส่งไปยังอีเมลของคุณแล้ว" if language == 'th' else "OTP has been sent to your email"
        },
        language=language
    )


@extend_schema(
    summary="Verify OTP",
    description="Verify OTP code with the token",
    request={
        "type": "object",
        "properties": {
            "token": {"type": "string", "description": "JWT token from generate_otp"},
            "otp": {"type": "string", "description": "OTP code"},
            "ref_code": {"type": "string", "description": "Reference code"}
        },
        "required": ["token", "otp", "ref_code"]
    },
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "token": {"type": "string"},
                        "verified_at": {"type": "string", "format": "date-time"},
                        "message": {"type": "string", "example": "OTP verified successfully"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "Invalid OTP"},
                "error_code": {"type": "integer", "example": 2023},
                "data": {
                    "type": "object",
                    "properties": {
                        "token": {"type": "string"},
                        "attempts": {"type": "integer", "example": 1},
                        "remaining_attempts": {"type": "integer", "example": 2}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["OTP"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def verify_otp(request):
    """
    API สำหรับตรวจสอบ OTP
    """
    logger.info("Verify OTP request received")
    language = get_language_from_request(request)

    # ตรวจสอบข้อมูลที่ส่งมา
    token = request.data.get('token')
    otp_code = request.data.get('otp')
    ref_code = request.data.get('ref_code')

    if not token or not otp_code or not ref_code:
        logger.error("Token, OTP code, or ref_code not provided")
        return APIResponse.error(
            error_code=2001,  # Required field missing
            data={},
            language=language
        )

    # ตรวจสอบ OTP
    verification_result = OTPService.verify_otp(token, otp_code, ref_code)

    if verification_result['success']:
        # OTP ถูกต้อง
        logger.info("OTP verification successful")
        return APIResponse.success(
            data={
                "token": verification_result['data']['token'],
                "verified_at": verification_result['data']['verified_at'].isoformat(),
                "message": "OTP verified successfully"
            },
            language=language
        )
    else:
        # OTP ไม่ถูกต้อง
        error_code = verification_result.get('error_code', 3000)
        logger.warning(
            f"OTP verification failed with error code: {error_code}")

        # Get response data if available
        response_data = {}
        if 'data' in verification_result and verification_result['data']:
            response_data = verification_result['data']

        # Log the full verification_result for debugging
        logger.debug(f"Full verification result: {verification_result}")

        return APIResponse.error(
            error_code=error_code,
            data=response_data,
            language=language
        )


@extend_schema(
    summary="Validate OTP Token",
    description="Validate an already verified OTP token",
    request={
        "type": "object",
        "properties": {
            "token": {"type": "string", "description": "Verified JWT token"}
        },
        "required": ["token"]
    },
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "identifier": {"type": "string"},
                        "purpose": {"type": "string"},
                        "verified_at": {"type": "string", "format": "date-time"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "Token has not been verified"},
                "error_code": {"type": "integer", "example": 2026},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["OTP"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def validate_otp_token(request):
    """
    API สำหรับตรวจสอบความถูกต้องของ token ที่ผ่านการยืนยัน OTP แล้ว
    """
    logger.info("Validate OTP token request received")
    language = get_language_from_request(request)

    # ตรวจสอบข้อมูลที่ส่งมา
    token = request.data.get('token')

    if not token:
        logger.error("Token not provided")
        return APIResponse.error(
            error_code=2001,  # Required field missing
            data={},
            language=language
        )

    # ตรวจสอบ token
    validation_result = OTPService.validate_verified_token(token)

    if validation_result['success']:
        # Token ถูกต้องและได้รับการยืนยันแล้ว
        logger.info("Token validation successful")
        return APIResponse.success(
            data=validation_result['data'],
            language=language
        )
    else:
        # Token ไม่ถูกต้องหรือยังไม่ได้รับการยืนยัน
        logger.warning(
            f"Token validation failed: {validation_result['message']}")
        return APIResponse.error(
            error_code=validation_result['error_code'],
            data={},
            language=language,
            custom_message=validation_result['message']
        )


@extend_schema(
    tags=["Member Type"]
)
class TcdAppMasMemberTypeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Member Type (อ่านอย่างเดียว)
    """
    queryset = TcdAppMasMemberType.objects.all()
    serializer_class = TcdAppMasMemberTypeSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]


@extend_schema(
    tags=["Government Sector"]
)
class TcdAppMasGovernmentSectorViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Government Sector (อ่านอย่างเดียว)
    """
    queryset = TcdAppMasGovernmentSector.objects.all()
    serializer_class = TcdAppMasGovernmentSectorSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]


@extend_schema(
    tags=["Ministry"]
)
class TcdAppMasMinistryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Ministry (อ่านอย่างเดียว)
    """
    queryset = TcdAppMasMinistry.objects.all()
    serializer_class = TcdAppMasMinistrySerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]
    
    @action(detail=False, methods=['get'], url_path=r'list-by-government-sector/(?P<government_sector_id>\d+)')
    def list_by_government_sector(self, request, *args, **kwargs):
        """
        List ministries by government sector
        """
        government_sector_id = kwargs.get('government_sector_id', None)
        if government_sector_id is not None:
            queryset = self.filter_queryset(self.get_queryset())
            queryset = queryset.filter(app_mas_government_sector_id=government_sector_id)
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)


@extend_schema(
    tags=["Department"]
)
class TcdAppMasDepartmentViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Department (อ่านอย่างเดียว)
    """
    queryset = TcdAppMasDepartment.objects.all()
    serializer_class = TcdAppMasDepartmentSerializer
    pagination_class = CustomPagination
    parser_classes = (JSONParser,)
    permission_classes = [AllowAny]

    @action(detail=False, methods=['get'], url_path=r'list-by-ministry/(?P<ministry_id>\d+)')
    def list_by_ministry(self, request, *args, **kwargs):
        """
        List departments by ministry
        """
        ministry_id = kwargs.get('ministry_id', None)
        if ministry_id is not None:
            queryset = self.filter_queryset(self.get_queryset())
            queryset = queryset.filter(app_mas_ministry_id=ministry_id)
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)


@extend_schema(
    summary="Request Password Reset",
    description="ขอรีเซ็ตรหัสผ่านโดยส่ง OTP ไปยังอีเมล",
    request=PasswordResetRequestSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "token": {"type": "string"},
                        "ref_code": {"type": "string"},
                        "expires_at": {"type": "string", "format": "date-time"},
                        "message": {"type": "string", "example": "รหัส OTP สำหรับรีเซ็ตรหัสผ่านได้ถูกส่งไปยังอีเมลของคุณแล้ว"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบอีเมลนี้ในระบบ"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Password Reset"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def request_password_reset(request):
    """
    API สำหรับขอรีเซ็ตรหัสผ่าน
    ส่ง OTP ไปยังอีเมลของผู้ใช้
    """
    logger.info("Password reset request received")
    language = get_language_from_request(request)

    # Validate input data
    serializer = PasswordResetRequestSerializer(data=request.data)

    if not serializer.is_valid():
        logger.error("Password reset request validation failed")
        logger.info(f"Serializer errors: {serializer.errors}")

        return APIResponse.smart_validation_error(
            errors=serializer.errors,
            language=language
        )

    email = serializer.validated_data['email']
    logger.info(f"Processing password reset request for email: {email}")

    try:
        # Use PasswordResetService to handle the request
        from authentication.services import PasswordResetService

        reset_result = PasswordResetService.request_password_reset(
            email=email,
            language=language
        )

        if reset_result['success']:
            logger.info(f"Password reset request successful for email: {email}")

            return APIResponse.success(
                data=reset_result['data'],
                language=language
            )
        else:
            logger.error(f"Password reset request failed for email: {email} - Error: {reset_result.get('error_code')}")

            return APIResponse.error(
                error_code=reset_result['error_code'],
                data={},
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error during password reset request: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


@extend_schema(
    summary="Verify Password Reset OTP",
    description="ตรวจสอบ OTP สำหรับการรีเซ็ตรหัสผ่าน",
    request=PasswordResetVerifyOTPSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "verified": {"type": "boolean", "example": True},
                        "verified_token": {"type": "string"},
                        "message": {"type": "string", "example": "ตรวจสอบ OTP สำเร็จ"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "รหัส OTP ไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 2023},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Password Reset"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def verify_password_reset_otp(request):
    """
    API สำหรับตรวจสอบ OTP ในการรีเซ็ตรหัสผ่าน
    """
    logger.info("Password reset OTP verification request received")
    language = get_language_from_request(request)

    # Validate input data
    serializer = PasswordResetVerifyOTPSerializer(data=request.data)

    if not serializer.is_valid():
        logger.error("Password reset OTP verification validation failed")
        logger.info(f"Serializer errors: {serializer.errors}")

        return APIResponse.smart_validation_error(
            errors=serializer.errors,
            language=language
        )

    validated_data = serializer.validated_data
    logger.info(f"Processing password reset OTP verification for email: {validated_data['email']}")

    try:
        # Use PasswordResetService to verify OTP
        from authentication.services import PasswordResetService

        verification_result = PasswordResetService.verify_password_reset_otp(
            email=validated_data['email'],
            otp_token=validated_data['otp_token'],
            otp_code=validated_data['otp'],
            ref_code=validated_data['ref_code'],
            language=language
        )

        if verification_result['success']:
            logger.info(f"Password reset OTP verification successful for email: {validated_data['email']}")

            # Generate a verified token for the next step
            import jwt
            from django.conf import settings
            from django.utils import timezone

            # Create a verified token that can be used for password update
            verified_token_payload = {
                'email': validated_data['email'],
                'purpose': 'password_reset_verified',
                'verified_at': timezone.now().timestamp(),
                'expires_at': (timezone.now() + timezone.timedelta(minutes=10)).timestamp()  # 10 minutes to complete
            }

            verified_token = jwt.encode(
                verified_token_payload,
                settings.JWT_PRIVATE_KEY,
                algorithm='RS256'
            )

            return APIResponse.success(
                data={
                    "verified": True,
                    "verified_token": verified_token,
                    "message": "ตรวจสอบ OTP สำเร็จ กรุณาดำเนินการตั้งรหัสผ่านใหม่ภายใน 10 นาที" if language == 'th' else "OTP verified successfully. Please proceed to set new password within 10 minutes"
                },
                language=language
            )
        else:
            logger.error(f"Password reset OTP verification failed for email: {validated_data['email']} - Error: {verification_result.get('error_code')}")

            return APIResponse.error(
                error_code=verification_result['error_code'],
                data=verification_result.get('data', {}),
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error during password reset OTP verification: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


@extend_schema(
    summary="Update Password Reset",
    description="อัปเดตรหัสผ่านใหม่หลังจากตรวจสอบ OTP สำเร็จ",
    request=PasswordResetUpdatePasswordSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "รีเซ็ตรหัสผ่านสำเร็จ"},
                        "user_type": {"type": "string", "example": "member"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "Token ไม่ถูกต้องหรือหมดอายุ"},
                "error_code": {"type": "integer", "example": 2025},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Password Reset"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def update_password_reset(request):
    """
    API สำหรับอัปเดตรหัสผ่านใหม่หลังจากตรวจสอบ OTP สำเร็จ
    """
    logger.info("Password reset update request received")
    language = get_language_from_request(request)

    # Validate input data
    serializer = PasswordResetUpdatePasswordSerializer(data=request.data)

    if not serializer.is_valid():
        logger.error("Password reset update validation failed")
        logger.info(f"Serializer errors: {serializer.errors}")

        return APIResponse.smart_validation_error(
            errors=serializer.errors,
            language=language
        )

    validated_data = serializer.validated_data
    logger.info(f"Processing password reset update for email: {validated_data['email']}")

    try:
        # Verify the verified token
        import jwt
        from django.conf import settings
        from django.utils import timezone

        try:
            verified_payload = jwt.decode(
                validated_data['verified_token'],
                settings.JWT_PUBLIC_KEY,
                algorithms=['RS256']
            )

            # Check if token is for password reset
            if verified_payload.get('purpose') != 'password_reset_verified':
                logger.error(f"Invalid verified token purpose: {verified_payload.get('purpose')}")
                return APIResponse.error(
                    error_code=2025,  # Invalid token
                    data={},
                    language=language
                )

            # Check if email matches
            if verified_payload.get('email') != validated_data['email']:
                logger.error(f"Email mismatch in verified token: {validated_data['email']} vs {verified_payload.get('email')}")
                return APIResponse.error(
                    error_code=2025,  # Invalid token
                    data={},
                    language=language
                )

            # Check if token is expired
            current_time = timezone.now().timestamp()
            if verified_payload.get('expires_at', 0) < current_time:
                logger.error(f"Verified token expired for email: {validated_data['email']}")
                return APIResponse.error(
                    error_code=2021,  # Token expired
                    data={},
                    language=language
                )

        except jwt.InvalidTokenError as e:
            logger.error(f"Invalid verified token: {str(e)}")
            return APIResponse.error(
                error_code=2025,  # Invalid token
                data={},
                language=language
            )

        # Use PasswordResetService to update password
        from authentication.services import PasswordResetService

        update_result = PasswordResetService.update_user_password(
            email=validated_data['email'],
            new_password=validated_data['new_password'],
            language=language
        )

        if update_result['success']:
            logger.info(f"Password reset update successful for email: {validated_data['email']}")

            user_type = update_result['data']['user_type']

            return APIResponse.success(
                data={
                    "message": "รีเซ็ตรหัสผ่านสำเร็จ" if language == 'th' else "Password reset successful",
                    "user_type": user_type
                },
                language=language
            )
        else:
            logger.error(f"Password reset update failed for email: {validated_data['email']} - Error: {update_result.get('error_code')}")

            return APIResponse.error(
                error_code=update_result['error_code'],
                data=update_result.get('data', {}),
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error during password reset update: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


@extend_schema(
    summary="Change Password",
    description="เปลี่ยนรหัสผ่านสำหรับผู้ใช้ที่ล็อกอินแล้ว (ไม่ต้องผ่าน OTP)",
    request=ChangePasswordSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "เปลี่ยนรหัสผ่านสำเร็จ"},
                        "user_type": {"type": "string", "example": "member"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "รหัสผ่านไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 1001},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Member"]
)
@api_view(['POST'])
def change_password(request):
    """
    API สำหรับเปลี่ยนรหัสผ่าน (ไม่ต้องผ่าน OTP)
    ต้องมี JWT token ในการเข้าถึง
    """
    logger.info("Change password request received")
    language = get_language_from_request(request)

    # ตรวจสอบการ authenticate
    if not request.user or not hasattr(request.user, 'is_authenticated') or not request.user.is_authenticated:
        logger.error("User not authenticated")
        return APIResponse.unauthorized(
            error_code=1000,  # Unauthorized
            language=language
        )

    # Validate input data
    serializer = ChangePasswordSerializer(data=request.data)

    if not serializer.is_valid():
        logger.error("Change password validation failed")
        logger.info(f"Serializer errors: {serializer.errors}")

        return APIResponse.smart_validation_error(
            errors=serializer.errors,
            language=language
        )

    validated_data = serializer.validated_data
    logger.info(f"Processing change password for user_id: {request.user.id}")

    try:
        # ดึงข้อมูลผู้ใช้จาก JWT token
        user_id = getattr(request.user, 'id', None)
        user_type = getattr(request.user, 'user_type', None)

        if not user_id:
            logger.error("Invalid user ID from JWT token")
            return APIResponse.unauthorized(
                error_code=1000,  # Unauthorized
                language=language
            )

        # ตรวจสอบประเภทผู้ใช้จาก JWT token หรือจากชื่อ class
        if not user_type:
            # ถ้าไม่มี user_type ใน token ให้ดูจากชื่อ class ของ user object
            user_class_name = request.user.__class__.__name__
            if user_class_name == 'TcdAppMember':
                user_type = 'member'
            elif user_class_name == 'TcdUserConsult':
                user_type = 'consultant'
            else:
                logger.error(f"Unknown user class: {user_class_name}")
                return APIResponse.unauthorized(
                    error_code=1000,  # Unauthorized
                    language=language
                )

        logger.info(f"Change password request for user_type: {user_type}, user_id: {user_id}")

        # Use ChangePasswordService to change password
        from authentication.services import ChangePasswordService

        change_result = ChangePasswordService.change_user_password(
            user_id=user_id,
            user_type=user_type,
            current_password=validated_data['current_password'],
            new_password=validated_data['new_password'],
            request=request,
            language=language
        )

        if change_result['success']:
            logger.info(f"Password change successful for user_id: {user_id}")

            return APIResponse.success(
                data=change_result['data'],
                language=language
            )
        else:
            logger.error(f"Password change failed for user_id: {user_id} - Error: {change_result.get('error_code')}")

            return APIResponse.error(
                error_code=change_result['error_code'],
                data=change_result.get('data', {}),
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error during password change: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


@extend_schema(
    summary="Delete Member Account",
    description="ลบบัญชีสมาชิก Member โดยเช็คจาก token ว่าเป็นตัวเองเท่านั้นที่ลบได้ พร้อมตรวจสอบรหัสผ่าน",
    request=DeleteMemberSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "ลบสมาชิกสำเร็จ"},
                        "member_id": {"type": "integer", "example": 123},
                        "username": {"type": "string", "example": "john_doe"},
                        "deleted_at": {"type": "string", "example": "2024-01-01T12:00:00Z"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "รหัสผ่านไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 1007},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        404: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบข้อมูลผู้ใช้"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Member"]
)
@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
def delete_member(request):
    """
    API สำหรับลบบัญชีสมาชิก Member

    Features:
    - เช็คจาก JWT token ว่าเป็นตัวเองเท่านั้นที่ลบได้
    - ต้องระบุ current_password เพื่อยืนยันตัวตน
    - ลบโดยการเปลี่ยนสถานะเป็น DELETED แทนการลบจริง
    - ต้องมี JWT token ในการเข้าถึง
    - เฉพาะ Member เท่านั้นที่สามารถใช้ API นี้ได้
    
    ตัวอย่างการใช้งาน:
    
    DELETE /api/auth/member/delete/
    Authorization: Bearer <access_token>
    Content-Type: application/json
    
    {
        "current_password": "current_password_here"
    }
    
    หมายเหตุ:
    - current_password: จำเป็นต้องระบุเพื่อยืนยันตัวตน
    """
    logger.info("Delete member request received")
    language = get_language_from_request(request)

    # ตรวจสอบการ authenticate
    if not request.user or not hasattr(request.user, 'is_authenticated') or not request.user.is_authenticated:
        logger.error("User not authenticated")
        return APIResponse.unauthorized(
            error_code=1000,  # Unauthorized
            language=language
        )

    try:
        # ดึงข้อมูลผู้ใช้จาก JWT token
        user_id = getattr(request.user, 'id', None)
        user_type = getattr(request.user, 'user_type', None)

        if not user_id:
            logger.error("Invalid user ID from JWT token")
            return APIResponse.unauthorized(
                error_code=1000,  # Unauthorized
                language=language
            )

        # ตรวจสอบประเภทผู้ใช้จาก JWT token หรือจากชื่อ class
        if not user_type:
            # ถ้าไม่มี user_type ใน token ให้ดูจากชื่อ class ของ user object
            user_class_name = request.user.__class__.__name__
            if user_class_name == 'TcdAppMember':
                user_type = 'member'
            elif user_class_name == 'TcdUserConsult':
                user_type = 'consultant'
            else:
                logger.error(f"Unknown user class: {user_class_name}")
                return APIResponse.unauthorized(
                    error_code=1000,  # Unauthorized
                    language=language
                )

        # ตรวจสอบว่าเป็น Member เท่านั้น
        if user_type != 'member':
            logger.error(f"Invalid user type for member deletion: {user_type}")
            return APIResponse.unauthorized(
                error_code=1006,  # Access denied
                language=language
            )

        logger.info(f"Delete member request for user_id: {user_id}")

        # Validate input data using DeleteMemberSerializer
        serializer = DeleteMemberSerializer(data=request.data)

        if not serializer.is_valid():
            logger.error("Delete member validation failed")
            logger.info(f"Serializer errors: {serializer.errors}")

            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )

        validated_data = serializer.validated_data
        current_password = validated_data['current_password']
        logger.info(f"Processing member deletion for user_id: {user_id}")

        # Use DeleteMemberService to delete member
        from authentication.services import DeleteMemberService

        delete_result = DeleteMemberService.delete_member(
            user_id=user_id,
            current_password=current_password,
            language=language,
            request=request
        )

        if delete_result['success']:
            logger.info(f"Member deletion successful for user_id: {user_id}")

            return APIResponse.success(
                data=delete_result['data'],
                language=language
            )
        else:
            logger.error(f"Member deletion failed for user_id: {user_id} - Error: {delete_result.get('error_code')}")

            # ตรวจสอบ error code เพื่อกำหนด status code ที่เหมาะสม
            status_code = 400
            if delete_result.get('error_code') == 1000:  # User not found
                status_code = 404
            elif delete_result.get('error_code') == 1006:  # Access denied
                status_code = 401

            return APIResponse.error(
                error_code=delete_result['error_code'],
                data=delete_result.get('data', {}),
                language=language,
                status_code=status_code
            )

    except Exception as e:
        logger.error(f"Unexpected error during member deletion: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


@extend_schema(
    summary="Update Member Info",
    description="อัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน",
    request=UpdateMemberInfoSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "อัปเดตข้อมูลสมาชิกสำเร็จ"},
                        "member": {"type": "object"},
                        "updated_fields": {"type": "array", "items": {"type": "string"}}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "รหัสผ่านไม่ถูกต้อง"},
                "error_code": {"type": "integer", "example": 1001},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Member"]
)
@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_member_info(request):
    """
    API สำหรับอัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน
    
    Features:
    - ต้องมี JWT token ในการเข้าถึง
    - ต้องระบุ current_password เพื่อยืนยันตัวตน
    - เฉพาะ Member เท่านั้นที่สามารถใช้ API นี้ได้
    - สามารถอัปเดตข้อมูลได้เฉพาะของตัวเอง
    - รองรับการอัปเดตฟิลด์ต่างๆ ของ Member
    
    ตัวอย่างการใช้งาน:
    
    PUT /api/auth/member/update-info/
    Authorization: Bearer <access_token>
    Content-Type: application/json
    
    {
        "current_password": "current_password_here",
        "first_name": "ชื่อใหม่",
        "last_name": "นามสกุลใหม่",
        "email": "<EMAIL>",
        "phone": "0987654321",
        "website": "https://newwebsite.com",
        "is_notification": "Y",
        "lang": "th"
    }
    
    หมายเหตุ:
    - current_password: จำเป็นต้องระบุเพื่อยืนยันตัวตน
    - ฟิลด์อื่นๆ เป็น optional สามารถส่งเฉพาะที่ต้องการอัปเดต
    - email, phone, identity_card_no จะตรวจสอบความซ้ำกับสมาชิกคนอื่น
    - foreign key fields ใช้รูปแบบ *_id เช่น app_mas_member_type_id
    """
    logger.info("Update member info request received")
    language = get_language_from_request(request)

    # ตรวจสอบการ authenticate
    if not request.user or not hasattr(request.user, 'is_authenticated') or not request.user.is_authenticated:
        logger.error("User not authenticated")
        return APIResponse.unauthorized(
            error_code=1000,  # Unauthorized
            language=language
        )

    try:
        # ดึงข้อมูลผู้ใช้จาก JWT token
        user_id = getattr(request.user, 'id', None)
        user_type = getattr(request.user, 'user_type', None)

        if not user_id:
            logger.error("Invalid user ID from JWT token")
            return APIResponse.unauthorized(
                error_code=1000,  # Unauthorized
                language=language
            )

        # ตรวจสอบประเภทผู้ใช้จาก JWT token หรือจากชื่อ class
        if not user_type:
            # ถ้าไม่มี user_type ใน token ให้ดูจากชื่อ class ของ user object
            user_class_name = request.user.__class__.__name__
            if user_class_name == 'TcdAppMember':
                user_type = 'member'
            elif user_class_name == 'TcdUserConsult':
                user_type = 'consultant'
            else:
                logger.error(f"Unknown user class: {user_class_name}")
                return APIResponse.unauthorized(
                    error_code=1000,  # Unauthorized
                    language=language
                )

        # ตรวจสอบว่าเป็น Member เท่านั้น
        if user_type != 'member':
            logger.error(f"Invalid user type for member info update: {user_type}")
            return APIResponse.unauthorized(
                error_code=1006,  # Access denied
                language=language
            )

        logger.info(f"Update member info request for user_id: {user_id}")

        # Validate input data using UpdateMemberInfoSerializer
        serializer = UpdateMemberInfoSerializer(data=request.data, context={'request': request})

        if not serializer.is_valid():
            logger.error("Update member info validation failed")
            logger.info(f"Serializer errors: {serializer.errors}")

            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )

        validated_data = serializer.validated_data
        logger.info(f"Processing member info update for user_id: {user_id}")

        # Use UpdateMemberInfoService to update member info
        update_result = UpdateMemberInfoService.update_member_info(
            user_id=user_id,
            validated_data=validated_data,
            language=language
        )

        if update_result['success']:
            logger.info(f"Member info update successful for user_id: {user_id}")

            return APIResponse.success(
                data=update_result['data'],
                language=language
            )
        else:
            logger.error(f"Member info update failed for user_id: {user_id} - Error: {update_result.get('error_code')}")

            return APIResponse.error(
                error_code=update_result['error_code'],
                data=update_result.get('data', {}),
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error during member info update: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


@extend_schema(
    summary="Update Member Language",
    description="อัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน",
    request=UpdateMemberLangSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "message": {"type": "string", "example": "อัปเดตภาษาสำเร็จ"},
                        "member": {"type": "object"},
                        "updated_fields": {"type": "array", "items": {"type": "string"}},
                        "old_lang": {"type": "string", "example": "th"},
                        "new_lang": {"type": "string", "example": "en"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ภาษาต้องเป็น 'th' หรือ 'en' เท่านั้น"},
                "error_code": {"type": "integer", "example": 2001},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "status": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Member"]
)
@api_view(['PATCH'])
@permission_classes([IsAuthenticated])
def update_member_lang(request):
    """
    API สำหรับอัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน
    
    Features:
    - ต้องมี JWT token ในการเข้าถึง
    - ไม่ต้องระบุ current_password
    - เฉพาะ Member เท่านั้นที่สามารถใช้ API นี้ได้
    - สามารถอัปเดตภาษาได้เฉพาะของตัวเอง
    - รองรับเฉพาะ 'th' และ 'en'
    
    ตัวอย่างการใช้งาน:
    
    PATCH /api/auth/member/update-lang/
    Authorization: Bearer <access_token>
    Content-Type: application/json
    
    {
        "lang": "en"
    }
    
    หมายเหตุ:
    - lang: จำเป็นต้องระบุ ('th' หรือ 'en')
    """
    logger.info("Update member language request received")
    language = get_language_from_request(request)

    # ตรวจสอบการ authenticate
    if not request.user or not hasattr(request.user, 'is_authenticated') or not request.user.is_authenticated:
        logger.error("User not authenticated")
        return APIResponse.unauthorized(
            error_code=1000,  # Unauthorized
            language=language
        )

    try:
        # ดึงข้อมูลผู้ใช้จาก JWT token
        user_id = getattr(request.user, 'id', None)
        user_type = getattr(request.user, 'user_type', None)

        if not user_id:
            logger.error("Invalid user ID from JWT token")
            return APIResponse.unauthorized(
                error_code=1000,  # Unauthorized
                language=language
            )

        # ตรวจสอบประเภทผู้ใช้จาก JWT token หรือจากชื่อ class
        if not user_type:
            # ถ้าไม่มี user_type ใน token ให้ดูจากชื่อ class ของ user object
            user_class_name = request.user.__class__.__name__
            if user_class_name == 'TcdAppMember':
                user_type = 'member'
            elif user_class_name == 'TcdUserConsult':
                user_type = 'consultant'
            else:
                logger.error(f"Unknown user class: {user_class_name}")
                return APIResponse.unauthorized(
                    error_code=1000,  # Unauthorized
                    language=language
                )

        # ตรวจสอบว่าเป็น Member หรือ Consultant เท่านั้น
        if user_type != 'member' and user_type != 'consultant':
            logger.error(f"Invalid user type for member language update: {user_type}")
            return APIResponse.unauthorized(
                error_code=1006,  # Access denied
                language=language
            )

        logger.info(f"Update member language request for user_id: {user_id}")

        # Validate input data using UpdateMemberLangSerializer
        serializer = UpdateMemberLangSerializer(data=request.data)

        if not serializer.is_valid():
            logger.error("Update member language validation failed")
            logger.info(f"Serializer errors: {serializer.errors}")

            return APIResponse.smart_validation_error(
                errors=serializer.errors,
                language=language
            )

        validated_data = serializer.validated_data
        new_lang = validated_data['lang']
        logger.info(f"Processing member language update for user_id: {user_id}, new lang: {new_lang}")

        if user_type == 'member':
            # Use UpdateMemberLangService to update member language
            update_result = UpdateMemberLangService.update_member_lang(
                user_id=user_id,
                lang=new_lang,
                language=language
            )
        elif user_type == 'consultant':
            # Use UpdateConsultantLangService to update consultant language
            update_result = UpdateConsultantLangService.update_consultant_lang(
                user_id=user_id,
                lang=new_lang,
                language=language
            )

        if update_result['success']:
            logger.info(f"Member language update successful for user_id: {user_id}")

            return APIResponse.success(
                data=update_result['data'],
                language=language
            )
        else:
            logger.error(f"Member language update failed for user_id: {user_id} - Error: {update_result.get('error_code')}")

            return APIResponse.error(
                error_code=update_result['error_code'],
                data=update_result.get('data', {}),
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error during member language update: {str(e)}")
        return APIResponse.server_error(
            error_code=3000,  # Internal server error
            language=language
        )


class UpdateTokenAppSerializer(serializers.Serializer):
    token_app = serializers.CharField(
        help_text="token_app ใหม่",
        required=True,
        max_length=250,
    )


@extend_schema(
    summary="Update Token App",
    description="อัปเดต token_app ของผู้ใช้ (ดึง user_id และ user_type จาก JWT token) - รองรับทั้ง Member และ Consultant",
    request=UpdateTokenAppSerializer,
    responses={
        200: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": True},
                "error_message": {"type": "null"},
                "error_code": {"type": "null"},
                "data": {
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "integer", "example": 123},
                        "user_type": {"type": "string", "example": "member"},
                        "message": {"type": "string", "example": "อัปเดต token_app สำเร็จ"}
                    }
                },
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        400: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ข้อมูลไม่ครบถ้วน"},
                "error_code": {"type": "integer", "example": 1001},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        401: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่ได้รับอนุญาต"},
                "error_code": {"type": "integer", "example": 1000},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        },
        404: {
            "type": "object",
            "properties": {
                "success": {"type": "boolean", "example": False},
                "error_message": {"type": "string", "example": "ไม่พบข้อมูล"},
                "error_code": {"type": "integer", "example": 1002},
                "data": {"type": "object"},
                "api_version": {"type": "string", "example": "v.0.0.1"}
            }
        }
    },
    tags=["Token Management"]
)
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_token_app(request):
    """
    API สำหรับอัปเดต token_app ของผู้ใช้
    ดึง user_id และ user_type จาก JWT token
    รองรับทั้ง Member และ Consultant
    
    ตัวอย่างการใช้งาน:
    
    POST /api/update-token-app/
    Authorization: Bearer <jwt_token>
    Content-Type: application/json
    
    {
        "token_app": "new_token_app_value"
    }
    """
    try:
        # Get language from request
        language = get_language_from_request(request)
        
        # ตรวจสอบการ authenticate
        if not request.user or not hasattr(request.user, 'is_authenticated') or not request.user.is_authenticated:
            logger.error("User not authenticated")
            return APIResponse.unauthorized(
                error_code=1000,  # Unauthorized
                language=language
            )
        
        # ดึงข้อมูลผู้ใช้จาก JWT token
        user_id = getattr(request.user, 'id', None)
        user_type = getattr(request.user, 'user_type', None)
        
        if not user_id:
            logger.error("Invalid user ID from JWT token")
            return APIResponse.unauthorized(
                error_code=1000,  # Unauthorized
                language=language
            )
        
        # ตรวจสอบประเภทผู้ใช้
        if not user_type:
            user_class_name = request.user.__class__.__name__
            if user_class_name == 'TcdAppMember':
                user_type = 'member'
            elif user_class_name == 'TcdUserConsult':
                user_type = 'consultant'
            else:
                logger.error(f"Unknown user class: {user_class_name}")
                return APIResponse.unauthorized(
                    error_code=1000,  # Unauthorized
                    language=language
                )
        
        # ตรวจสอบว่าเป็น Member หรือ Consultant เท่านั้น
        if user_type not in ['member', 'consultant']:
            logger.error(f"Invalid user type for token update: {user_type}")
            return APIResponse.unauthorized(
                error_code=1006,  # Access denied
                language=language
            )
        
        # Validate request data
        serializer = UpdateTokenAppSerializer(data=request.data)
        if not serializer.is_valid():
            return APIResponse.validation_error(
                errors=serializer.errors,
                language=language
            )
        
        # Extract validated data
        token_app = serializer.validated_data['token_app']
        
        # Call unified service to update token_app
        result = UpdateTokenAppService.update_token_app(
            user_id=user_id,
            user_type=user_type,
            token_app=token_app,
            language=language
        )
        
        if result['success']:
            return APIResponse.success(
                data=result.get('data', {}),
                language=language
            )
        else:
            return APIResponse.error(
                error_code=result.get('error_code', 3000),
                custom_message=result.get('error_message'),
                language=language
            )
        
    except Exception as e:
        logger.error(f"Error in update_token_app view: {str(e)}")
        language = get_language_from_request(request)
        return APIResponse.server_error(
            error_code=5000,  # เกิดข้อผิดพลาดในระบบ
            language=language
        )


@extend_schema(
    tags=["Authentication"]
)
@api_view(['GET'])
@permission_classes([AllowAny])
def login_by_thaid_code(request):
    """
    API สำหรับลงชื่อเข้าใช้งานด้วย ThaID
    """
    try:
        language = get_language_from_request(request)
        code = request.query_params.get('code')
        user_type = request.query_params.get('user_type')
        if not code:
            return APIResponse.error(
                error_code=4000,
                language=language
            )

        # ThaID OAuth2 token endpoint
        THAID_BASE_AUTH_URL = getattr(settings, 'THAID_BASE_AUTH_URL')
        THAID_CALLBACK_URL = getattr(settings, 'THAID_CALLBACK_URL')
        THAID_CLIENT_ID = getattr(settings, 'THAID_CLIENT_ID')
        THAID_CLIENT_SECRET = getattr(settings, 'THAID_CLIENT_SECRET')
        
        token_url = THAID_BASE_AUTH_URL + 'token/'
        
        # Prepare the request data
        body = {
            'grant_type': 'authorization_code',
            'code': code,
            'redirect_uri': THAID_CALLBACK_URL
        }
        
        AUTH_STRING = f'{THAID_CLIENT_ID}:{THAID_CLIENT_SECRET}'
        AUTH_STRING_BASE64 = base64.b64encode(AUTH_STRING.encode()).decode()
        
        # Prepare headers
        headers = {
            'Authorization': f'Basic {AUTH_STRING_BASE64}',
            'Content-type': 'application/x-www-form-urlencoded',
        }
        
        try:
            # Make request to ThaID token endpoint
            response = requests.post(
                token_url,
                data=urlencode(body),
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                token_response = response.json()
                # logger.info(f"Successfully got token from ThaID: {token_response}")
                
                # Get user info from ThaID using the access token
                introspect_url = THAID_BASE_AUTH_URL + 'introspect/'
                
                introspect_body = {
                    'token': f"Bearer {token_response['access_token']}"
                }
                
                introspect_headers = {
                    'Authorization': f'Basic {AUTH_STRING_BASE64}',
                    'Content-type': 'application/x-www-form-urlencoded',
                }
                
                try:
                    introspect_response = requests.post(
                        introspect_url,
                        data=urlencode(introspect_body),
                        headers=introspect_headers,
                        timeout=30
                    )
                    
                    if introspect_response.status_code == 200:
                        user_info = introspect_response.json()
                        logger.info(f"Successfully got user info from ThaID: {user_info}")
                        
                        # Extract user information
                        pid = user_info.get('sub')
                        
                        if not pid:
                            err_msg = "No PID found in ThaID response"
                            logger.error(err_msg)
                            return APIResponse.error(
                                error_code=4005,
                                language=language
                            )
                        
                        # Find in local database
                        try:
                            logger.info(f"User type: {user_type}")
                            if user_type == 'member':
                                user = TcdAppMember.objects.filter(identity_card_no=pid).first()
                                logger.info(f"User: {user}")
                                if not user:
                                    return APIResponse.error(
                                        error_code=4006,
                                        language=language
                                    )
                                user_profile = MemberAuthService.get_member_profile(user)
                                tokens = MemberAuthService.generate_tokens(user)
                            elif user_type == 'consultant':
                                consult_team = TcdUserConsultTeam.objects.filter(identity_card_no=pid).first()
                                logger.info(f"Consult team: {consult_team}")
                                if consult_team:
                                    logger.info(f"Consultant team found: {consult_team}")
                                    user = TcdUserConsult.objects.get(id=consult_team.user_consult_id)
                                    if not user:
                                        return APIResponse.error(
                                            error_code=4006,
                                            language=language
                                        )
                                    user_profile = ConsultantAuthService.get_consultant_profile(user)
                                    tokens = ConsultantAuthService.generate_tokens(user)
                                    
                                    # อัปเดต is_active_matching = 1 เมื่อ login สำเร็จ (ThaID)
                                    try:
                                        user.is_active_matching = 1
                                        user.save(update_fields=['is_active_matching'])
                                        logger.info(f"Updated is_active_matching to 1 for consultant via ThaID: {user.username}")
                                    except Exception as e:
                                        logger.error(f"Failed to update is_active_matching for consultant {user.username} via ThaID: {str(e)}")
                                        # ไม่ return error เพราะ login สำเร็จแล้ว
                                else:
                                    err_msg = f"Consultant team not found"
                                    logger.error(err_msg)
                                    return APIResponse.unauthorized(
                                        language=language
                                    )
                            else:
                                err_msg = f"Invalid user type: {user_type}"
                                logger.error(err_msg)
                                return APIResponse.error(
                                    error_code=4000,
                                    language=language
                                )
                            
                            if not user:
                                return APIResponse.unauthorized(
                                    language=language
                                )

                            logger.info(f"Found existing user with PID: {pid}")
                        except TcdAppMember.DoesNotExist:
                            return APIResponse.unauthorized(
                                language=language
                            )
                        except TcdUserConsult.DoesNotExist:
                            return APIResponse.unauthorized(
                                language=language
                            )
                        except Exception as e:
                            logger.error(f"Error creating user: {str(e)}")
                            return APIResponse.error(
                                error_code=5000,
                                language=language
                            )

                        TcdStatisticExternalLogin.objects.create(
                            type='A',
                            login_provider='T',
                            ip=get_client_ip(request),
                            date=timezone.now()
                        )

                        return APIResponse.success(
                            data={
                                'user': user_profile,
                                'tokens': tokens,
                                'session_info': {
                                    'login_time': timezone.now().isoformat(),
                                    'user_type': user_type
                                }
                            },
                            language=language
                        )
                        
                    else:
                        err_msg = f"Failed to get user info from ThaID. Status: {introspect_response.status_code}, Response: {introspect_response.text}"
                        logger.error(err_msg)
                        return APIResponse.error(
                            error_code=4004,
                            language=language
                        )
                        
                except requests.exceptions.RequestException as e:
                    err_msg = f"Request error when getting user info from ThaID: {str(e)}"
                    logger.error(err_msg)
                    return APIResponse.error(
                        error_code=4004,
                        language=language
                    )
                
            else:
                err_msg = f"Failed to get token from ThaID. Status: {response.status_code}, Response: {response.text}"
                logger.error(err_msg)
                return APIResponse.error(
                    error_code=4004,
                    language=language
                )
                
        except requests.exceptions.RequestException as e:
            err_msg = f"Request error when calling ThaID: {str(e)}"
            logger.error(err_msg)
            return APIResponse.error(
                error_code=4004,
                language=language
            )
        
    except Exception as e:
        logger.error(f"Error in login_by_thaid_code view: {str(e)}")
        language = get_language_from_request(request)
        return APIResponse.server_error(
            error_code=5000,  # เกิดข้อผิดพลาดในระบบ
            language=language
        )


@csrf_exempt
def callback_thaid(request):
    """
    หน้าเว็บสำหรับรับข้อมูลจาก ThaID หลังจากการลงชื่อเข้าใช้งาน (OAuth2 Callback)

    หน้านี้จะแสดงผลเป็น HTML page สำหรับ mobile app deep linking
    และจะส่งข้อมูลกลับไปยัง mobile app ผ่าน JavaScript
    """
    try:
        # Get OAuth2 callback parameters
        code = request.GET.get('code')
        state = request.GET.get('state')
        error = request.GET.get('error')
        error_description = request.GET.get('error_description')

        logger.info(f"ThaID callback received - code: {bool(code)}, state: {state}, error: {error}")

        # Prepare context for template
        context = {
            'callback_status': 'unknown',
            'code': code,
            'state': state,
            'error': error,
            'error_description': error_description,
            'title': 'ThaID Authentication',
            'app_name': 'MCDC'
        }

        # Handle OAuth2 error response
        if error:
            logger.warning(f"ThaID OAuth2 error: {error} - {error_description}")
            context.update({
                'callback_status': 'error',
                'message': 'การยืนยันตัวตนผ่าน ThaID ไม่สำเร็จ',
                'message_en': 'ThaID authentication failed',
                'details': error or 'OAuth2 authorization failed'
            })

        # Handle successful OAuth2 response
        elif code:
            logger.info(f"ThaID callback successful - authorization code received")
            context.update({
                'callback_status': 'success',
                'message': 'ยืนยันตัวตนผ่าน ThaID สำเร็จ',
                'message_en': 'ThaID authentication successful',
                'details': 'กำลังเปลี่ยนเส้นทางไปยังแอปพลิเคชัน...'
            })

        # Handle case where no code or error is provided
        else:
            logger.warning("ThaID callback received without code or error parameters")
            context.update({
                'callback_status': 'invalid',
                'message': 'ข้อมูลการยืนยันตัวตนไม่ถูกต้อง',
                'message_en': 'Invalid authentication data',
                'details': 'ไม่พบข้อมูลที่จำเป็นสำหรับการยืนยันตัวตน'
            })

        # Render HTML template
        return render(request, 'authentication/thaid_callback.html', context)

    except Exception as e:
        logger.error(f"Error in callback_thaid view: {str(e)}")

        # Return error page using template
        error_context = {
            'callback_status': 'error',
            'message': 'เกิดข้อผิดพลาด',
            'message_en': 'An error occurred',
            'details': 'ไม่สามารถประมวลผลการยืนยันตัวตนได้ กรุณาลองใหม่อีกครั้ง',
            'title': 'เกิดข้อผิดพลาด',
            'app_name': 'MCDC'
        }

        response = render(request, 'authentication/thaid_callback.html', error_context)
        response.status_code = 500
        return response


@extend_schema(
    summary="Track Daily App Usage",
    description="Records when users first open the app each day. Tracks unique IP addresses per day to monitor daily app usage statistics.",
    responses={
        200: OpenApiResponse(description="App usage tracked successfully or already tracked today"),
        400: OpenApiResponse(description="Invalid request data"),
        500: OpenApiResponse(description="Server error")
    },
    tags=["App Usage Tracking"]
)
@api_view(['POST'])
@permission_classes([AllowAny])
def track_app_usage(request):
    """
    ติดตามการใช้งานแอปพลิเคชันรายวัน

    บันทึกการเปิดแอปครั้งแรกของแต่ละ IP address ในแต่ละวัน
    ใช้สำหรับสถิติการใช้งานแอปพลิเคชัน

    **Logic:**
    - ตรวจสอบว่า IP address นี้ได้ถูกบันทึกในวันนี้แล้วหรือไม่
    - หากยังไม่ได้บันทึก จะสร้างรายการใหม่ใน app_statistic table
    - หากบันทึกแล้ว จะไม่บันทึกซ้ำ

    **Returns:**
    - tracked: true/false - ว่าได้บันทึกใหม่หรือไม่
    - reason: เหตุผลหากไม่ได้บันทึก
    - ip_address: IP address ที่ถูกติดตาม
    - date: วันที่บันทึก
    """
    try:
        # Get language from request
        language = get_language_from_request(request)

        # Call the service to track app usage
        result = AppUsageTrackingService.track_daily_app_usage(request, language)

        # Return appropriate response based on service result
        if result['success']:
            return APIResponse.success(
                data=result['data'],
                language=language
            )
        else:
            return APIResponse.error(
                error_code=result['error_code'],
                data=result.get('data', {}),
                language=language
            )

    except Exception as e:
        logger.error(f"Unexpected error in track_app_usage endpoint: {str(e)}")
        return APIResponse.server_error(
            language=get_language_from_request(request)
        )

